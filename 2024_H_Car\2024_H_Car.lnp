--cpu=Cortex-M4.fp.sp
"2024_h_car\startup_stm32f407xx.o"
"2024_h_car\main.o"
"2024_h_car\gpio.o"
"2024_h_car\dma.o"
"2024_h_car\i2c.o"
"2024_h_car\tim.o"
"2024_h_car\usart.o"
"2024_h_car\stm32f4xx_it.o"
"2024_h_car\stm32f4xx_hal_msp.o"
"2024_h_car\stm32f4xx_hal_i2c.o"
"2024_h_car\stm32f4xx_hal_i2c_ex.o"
"2024_h_car\stm32f4xx_hal_rcc.o"
"2024_h_car\stm32f4xx_hal_rcc_ex.o"
"2024_h_car\stm32f4xx_hal_flash.o"
"2024_h_car\stm32f4xx_hal_flash_ex.o"
"2024_h_car\stm32f4xx_hal_flash_ramfunc.o"
"2024_h_car\stm32f4xx_hal_gpio.o"
"2024_h_car\stm32f4xx_hal_dma_ex.o"
"2024_h_car\stm32f4xx_hal_dma.o"
"2024_h_car\stm32f4xx_hal_pwr.o"
"2024_h_car\stm32f4xx_hal_pwr_ex.o"
"2024_h_car\stm32f4xx_hal_cortex.o"
"2024_h_car\stm32f4xx_hal.o"
"2024_h_car\stm32f4xx_hal_exti.o"
"2024_h_car\stm32f4xx_hal_tim.o"
"2024_h_car\stm32f4xx_hal_tim_ex.o"
"2024_h_car\stm32f4xx_hal_uart.o"
"2024_h_car\system_stm32f4xx.o"
"2024_h_car\ebtn.o"
"2024_h_car\ringbuffer.o"
"2024_h_car\oled.o"
"2024_h_car\oled_font.o"
"2024_h_car\hardware_iic.o"
"2024_h_car\pid.o"
"2024_h_car\iic.o"
"2024_h_car\inv_mpu.o"
"2024_h_car\inv_mpu_dmp_motion_driver.o"
"2024_h_car\mpu6050.o"
"2024_h_car\encoder_driver.o"
"2024_h_car\key_driver.o"
"2024_h_car\led_driver.o"
"2024_h_car\motor_driver.o"
"2024_h_car\oled_driver.o"
"2024_h_car\uart_driver.o"
"2024_h_car\mpu6050_driver.o"
"2024_h_car\encoder_app.o"
"2024_h_car\gray_app.o"
"2024_h_car\key_app.o"
"2024_h_car\led_app.o"
"2024_h_car\motor_app.o"
"2024_h_car\oled_app.o"
"2024_h_car\pid_app.o"
"2024_h_car\uart_app.o"
"2024_h_car\mpu6050_app.o"
"2024_h_car\scheduler.o"
"2024_h_car\scheduler_task.o"
--strict --scatter "2024_H_Car\2024_H_Car.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "2024_H_Car.map" -o 2024_H_Car\2024_H_Car.axf