# MPU6050应用层移植指南

## 文档信息
- **版本**: v1.0
- **创建日期**: 2024-12-19
- **负责人**: <PERSON> (工程师)
- **项目**: 2024_H_Car MPU6050应用层移植

## 1. 应用层架构概述

### 1.1 核心文件结构
```
MPU6050应用层文件:
├── mpu6050_app.c/h          # 应用层接口
├── mpu6050_driver.c/h       # 驱动层(零漂校准+连续角度)
└── I2C通信层                # 硬件通信接口
```

### 1.2 关键API接口
```c
// 应用层主要接口
void Mpu6050_Init(void);     // MPU6050初始化
void Mpu6050_Task(void);     // 数据更新任务

// 全局角度变量
extern float Pitch, Roll, Yaw;  // 俯仰角、横滚角、航向角

// 驱动层关键函数
void MPU_Get_Gyro_Offset(short* gx_offset, short* gy_offset, short* gz_offset);
float convert_to_continuous_yaw(float current_yaw);
```

## 2. 核心移植代码

### 2.1 应用层初始化 (mpu6050_app.c)
```c
#include "mpu6050_app.h"

// 全局角度变量
float Pitch, Roll, Yaw;  // 俯仰、横滚、航向角

void Mpu6050_Init(void) {
    MPU_Init();        // 硬件初始化
    mpu_dmp_init();    // DMP初始化
}

void Mpu6050_Task(void) {
    // 获取DMP处理后的角度数据
    mpu_dmp_get_data(&Pitch, &Roll, &Yaw);
    
    // 转换为连续角度(解决±180°跳变)
    Yaw = convert_to_continuous_yaw(Yaw);
}
```

### 2.2 应用层头文件 (mpu6050_app.h)
```c
#ifndef __MPU6050_APP_H
#define __MPU6050_APP_H

#include "mpu6050_driver.h"
#include "inv_mpu.h"
#include "inv_mpu_dmp_motion_driver.h"

// 全局角度变量声明
extern float Pitch, Roll, Yaw;

// 应用层接口函数
void Mpu6050_Init(void);
void Mpu6050_Task(void);

#endif
```

## 3. 零漂校准算法

### 3.1 陀螺仪零漂校准实现
```c
// 获取陀螺仪零点偏移量(设备必须静止)
void MPU_Get_Gyro_Offset(short* gx_offset, short* gy_offset, short* gz_offset) {
    int i;
    short gx, gy, gz;
    long temp_gx = 0, temp_gy = 0, temp_gz = 0;

    // 400次采样求平均值(总计2秒校准时间)
    for(i = 0; i < 400; i++) {
        MPU_Get_Gyroscope(&gx, &gy, &gz);  // 读取原始陀螺仪数据
        temp_gx += gx;
        temp_gy += gy;
        temp_gz += gz;
        HAL_Delay(5);  // 5ms间隔采样
    }
    
    // 计算平均偏移量
    *gx_offset = temp_gx / 400;
    *gy_offset = temp_gy / 400;
    *gz_offset = temp_gz / 400;
}
```

### 3.2 零漂校准使用方法
```c
// 在系统启动时进行零漂校准
void System_Calibration(void) {
    short gx_offset, gy_offset, gz_offset;
    
    printf("开始陀螺仪校准，请保持设备静止...\n");
    
    // 获取零点偏移量
    MPU_Get_Gyro_Offset(&gx_offset, &gy_offset, &gz_offset);
    
    printf("校准完成: X=%d, Y=%d, Z=%d\n", gx_offset, gy_offset, gz_offset);
    
    // 将偏移量保存到EEPROM或Flash(可选)
    // Save_Calibration_Data(gx_offset, gy_offset, gz_offset);
}
```

## 4. 连续角度转换算法

### 4.1 连续角度算法核心实现
```c
// 静态变量保存状态
static float g_last_yaw = 0.0f;
static int g_revolution_count = 0;
static bool g_is_yaw_initialized = false;

/**
 * @brief 将[-180, 180]范围的角度转换为连续角度
 * @param current_yaw 当前角度值(-180 to 180)
 * @return 连续角度值(可超过±180°)
 */
float convert_to_continuous_yaw(float current_yaw) {
    const float WRAP_AROUND_THRESHOLD = 300.0f;  // 跳变检测阈值
    
    // 首次调用初始化
    if (!g_is_yaw_initialized) {
        g_last_yaw = current_yaw;
        g_is_yaw_initialized = true;
        g_revolution_count = 0;
        return current_yaw;
    }
    
    // 计算角度差值
    float diff = current_yaw - g_last_yaw;
    
    // 检测跳变并更新圈数
    if (diff > WRAP_AROUND_THRESHOLD) {
        // 从+180跳到-180，向左转过界
        g_revolution_count--;
    } else if (diff < -WRAP_AROUND_THRESHOLD) {
        // 从-180跳到+180，向右转过界
        g_revolution_count++;
    }
    
    // 更新上次角度值
    g_last_yaw = current_yaw;
    
    // 计算连续角度
    return current_yaw + (float)g_revolution_count * 360.0f;
}
```

### 4.2 连续角度算法原理说明
```c
// 算法工作原理示例:
// 
// 输入序列:     170° → 175° → -175° → -170° → 175° → 180°
// 差值:         --     5°     -350°    5°     345°    5°
// 检测结果:     --     正常    左转过界  正常   右转过界  正常
// 圈数变化:     0      0       -1       -1      0       0
// 连续角度:     170°   175°    185°     190°    535°    540°
//
// 关键参数:
// - WRAP_AROUND_THRESHOLD = 300°: 跳变检测阈值
// - 当|diff| > 300°时认为发生了跳变
// - diff > 300°: 从+180跳到-180 (左转过界，圈数-1)
// - diff < -300°: 从-180跳到+180 (右转过界，圈数+1)
```

## 5. I2C通信关键实现

### 5.1 I2C接口适配宏定义
```c
// 平台适配宏定义 (在mpu6050.h中)
#define delay_ms                HAL_Delay
#define MPU_IIC_Init           IIC_GPIO_Init
#define MPU_IIC_Start          IIC_Start
#define MPU_IIC_Stop           IIC_Stop
#define MPU_IIC_Send_Byte      IIC_Send_Byte
#define MPU_IIC_Read_Byte      IIC_Read_Byte
#define MPU_IIC_Wait_Ack       IIC_Wait_Ack
```

### 5.2 I2C GPIO配置 (关键部分)
```c
// GPIO引脚定义 (在IIC.c中修改)
#define GPIO_PORT_IIC     GPIOA                    // GPIO端口
#define RCC_IIC_ENABLE    __HAL_RCC_GPIOA_CLK_ENABLE()  // 时钟使能
#define IIC_SCL_PIN       GPIO_PIN_3               // SCL时钟线
#define IIC_SDA_PIN       GPIO_PIN_1               // SDA数据线

// GPIO操作宏
#define IIC_SCL_1()  HAL_GPIO_WritePin(GPIO_PORT_IIC, IIC_SCL_PIN, GPIO_PIN_SET)
#define IIC_SCL_0()  HAL_GPIO_WritePin(GPIO_PORT_IIC, IIC_SCL_PIN, GPIO_PIN_RESET)
#define IIC_SDA_1()  HAL_GPIO_WritePin(GPIO_PORT_IIC, IIC_SDA_PIN, GPIO_PIN_SET)
#define IIC_SDA_0()  HAL_GPIO_WritePin(GPIO_PORT_IIC, IIC_SDA_PIN, GPIO_PIN_RESET)
#define IIC_SDA_READ() HAL_GPIO_ReadPin(GPIO_PORT_IIC, IIC_SDA_PIN)
```

### 5.3 I2C初始化函数
```c
void IIC_GPIO_Init(void) {
    GPIO_InitTypeDef GPIO_InitStructure;
    
    RCC_IIC_ENABLE;  // 使能GPIO时钟
    
    // 配置SCL和SDA为开漏输出
    GPIO_InitStructure.Pin = IIC_SCL_PIN | IIC_SDA_PIN;
    GPIO_InitStructure.Speed = GPIO_SPEED_FREQ_HIGH;
    GPIO_InitStructure.Mode = GPIO_MODE_OUTPUT_OD;  // 开漏输出
    HAL_GPIO_Init(GPIO_PORT_IIC, &GPIO_InitStructure);
    
    // 初始化总线状态
    IIC_SCL_1();
    IIC_SDA_1();
}
```

## 6. 系统集成方法

### 6.1 主函数集成示例
```c
int main(void) {
    // 系统初始化
    HAL_Init();
    SystemClock_Config();
    
    // MPU6050初始化
    Mpu6050_Init();
    
    // 可选：零漂校准
    // System_Calibration();
    
    while(1) {
        // 定期更新角度数据 (建议5-10ms周期)
        Mpu6050_Task();
        
        // 使用角度数据
        printf("Pitch: %.2f°, Roll: %.2f°, Yaw: %.2f°\n", Pitch, Roll, Yaw);
        
        HAL_Delay(10);  // 10ms周期
    }
}
```

### 6.2 定时器中断集成
```c
// 在定时器中断中更新角度数据
void TIM_Callback(void) {
    static uint8_t mpu_counter = 0;
    
    if(++mpu_counter >= 5) {  // 5ms * 5 = 25ms周期
        mpu_counter = 0;
        Mpu6050_Task();       // 更新角度数据
    }
}
```

## 7. 移植步骤清单

### 7.1 必需文件清单
```
移植必需文件:
├── mpu6050_app.c/h          # 应用层接口
├── mpu6050_driver.c/h       # 零漂校准+连续角度
├── mpu6050.c/h              # 硬件抽象层
├── IIC.c/h                  # I2C通信层
└── DMP库文件 (如需要):
    ├── inv_mpu.c/h
    ├── inv_mpu_dmp_motion_driver.c/h
    └── dmpKey.h, dmpmap.h
```

### 7.2 移植步骤
1. **复制核心文件**: 将上述文件复制到新项目
2. **修改I2C引脚**: 在IIC.c中修改GPIO_PORT_IIC、IIC_SCL_PIN、IIC_SDA_PIN
3. **适配延时函数**: 确保delay_ms宏定义正确
4. **添加头文件路径**: 在编译器中添加包含路径
5. **调用初始化**: 在main函数中调用Mpu6050_Init()
6. **定期更新数据**: 调用Mpu6050_Task()更新角度

### 7.3 验证测试
```c
// 简单验证函数
void MPU6050_Test(void) {
    // 初始化
    Mpu6050_Init();
    HAL_Delay(100);
    
    // 读取10次数据验证
    for(int i = 0; i < 10; i++) {
        Mpu6050_Task();
        printf("Test %d: Pitch=%.1f, Roll=%.1f, Yaw=%.1f\n", 
               i+1, Pitch, Roll, Yaw);
        HAL_Delay(100);
    }
}
```

## 8. 常见问题解决

### 8.1 通信问题
- **现象**: 角度数据不更新或为0
- **解决**: 检查I2C引脚定义和上拉电阻(4.7KΩ)

### 8.2 角度跳变问题
- **现象**: 角度值在±180°附近跳变
- **解决**: 确保调用了convert_to_continuous_yaw()函数

### 8.3 零漂问题
- **现象**: 静止时角度缓慢漂移
- **解决**: 在设备静止时调用MPU_Get_Gyro_Offset()进行校准

## 9. 总结

本指南提供了MPU6050应用层的核心移植方法：

1. **简化架构**: 专注于应用层接口和关键算法
2. **核心功能**: 零漂校准和连续角度转换
3. **I2C通信**: 关键的硬件接口配置
4. **快速移植**: 清晰的步骤和验证方法

通过遵循本指南，可以快速将MPU6050应用层功能移植到新项目中。