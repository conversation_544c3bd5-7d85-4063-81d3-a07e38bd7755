# 灰度传感器使用指南

## 📋 **传感器概述**

### **硬件信息**
- **传感器型号**: 感为智能8路灰度传感器
- **通信协议**: I2C (硬件I2C3)
- **默认地址**: 0x4C (7位地址)
- **探头数量**: 8个红外探头
- **工作模式**: 数字模式 + 模拟模式

---

## 🔌 **接线方案**

### **I2C3硬件接线**
```
STM32F407VET6 引脚配置:
┌─────────────────┬─────────────────┬─────────────────┐
│   功能          │   STM32引脚     │   传感器引脚    │
├─────────────────┼─────────────────┼─────────────────┤
│   SCL (时钟)    │   PA8           │   SCL           │
│   SDA (数据)    │   PC9           │   SDA           │
│   VCC (电源)    │   3.3V/5V       │   VCC           │
│   GND (地线)    │   GND           │   GND           │
└─────────────────┴─────────────────┴─────────────────┘
```

### **I2C配置参数**
```c
// I2C3配置 (在i2c.c中)
hi2c3.Instance = I2C3;
hi2c3.Init.ClockSpeed = 100000;        // 100KHz时钟频率
hi2c3.Init.DutyCycle = I2C_DUTYCYCLE_2;
hi2c3.Init.AddressingMode = I2C_ADDRESSINGMODE_7BIT;
```

---

## 💻 **软件使用方法**

### **1. 初始化代码**
```c
// 在main.c中已自动初始化I2C3
void Gray_Init(void) {
    // 传感器自动初始化，无需额外配置
    // 可选：测试传感器连接
    if(Ping() == 0) {
        printf("灰度传感器连接成功\n");
    } else {
        printf("灰度传感器连接失败\n");
    }
}
```

### **2. 数据读取代码**
```c
void Gray_Task(void) {
    // 读取8位数字信号 (0=白色, 1=黑色)
    Digtal = ~IIC_Get_Digtal();  // 取反处理
    
    // 计算加权位置误差
    float weighted_sum = 0;
    uint8_t black_line_count = 0;
    
    for(uint8_t i = 0; i < 8; i++) {
        if((Digtal >> i) & 0x01) {  // 检测到黑线
            weighted_sum += gray_weights[i];
            black_line_count++;
        }
    }
    
    // 计算线位置误差 (用于PID控制)
    if(black_line_count > 0) {
        g_line_position_error = weighted_sum / (float)black_line_count;
    }
}
```

### **3. 权重配置**
```c
// 8路传感器权重表 (左负右正)
float gray_weights[8] = {
    -4.0f,  // 传感器1 (最左)
    -3.0f,  // 传感器2
    -2.0f,  // 传感器3
    -1.0f,  // 传感器4
     1.0f,  // 传感器5
     2.0f,  // 传感器6
     3.0f,  // 传感器7
     4.0f   // 传感器8 (最右)
};
```

---

## 🛠️ **核心API函数**

### **基础通信函数**
```c
// 测试传感器连接
unsigned char Ping(void);
// 返回: 0=连接成功, 1=连接失败

// 读取数字信号 (8位)
unsigned char IIC_Get_Digtal(void);
// 返回: 8位数据，每位代表一个传感器状态

// 读取模拟信号 (8路)
unsigned char IIC_Get_Anolog(unsigned char *Result, unsigned char len);
// 参数: Result=结果数组, len=读取长度
// 返回: 1=成功, 0=失败

// 读取单路模拟信号
unsigned char IIC_Get_Single_Anolog(unsigned char Channel);
// 参数: Channel=通道号(1-8)
// 返回: 模拟值(0-255)
```

### **高级功能函数**
```c
// 传感器归一化设置
unsigned char IIC_Anolog_Normalize(uint8_t Normalize_channel);

// 读取偏移值
unsigned short IIC_Get_Offset(void);

// 位操作宏
#define GET_NTH_BIT(sensor_value, nth_bit) \
    (((sensor_value) >> ((nth_bit)-1)) & 0x01)
```

---

## 🎯 **实际应用示例**

### **循迹控制集成**
```c
void Line_Following_Control(void) {
    // 1. 读取灰度传感器数据
    Gray_Task();
    
    // 2. 使用位置误差进行PID控制
    int line_pid_output = pid_calculate_positional(&pid_line, g_line_position_error);
    
    // 3. 应用到电机控制
    pid_set_target(&pid_speed_left,  basic_speed + line_pid_output);
    pid_set_target(&pid_speed_right, basic_speed - line_pid_output);
}
```

### **传感器状态监控**
```c
void Gray_Status_Monitor(void) {
    printf("传感器状态: ");
    for(int i = 0; i < 8; i++) {
        if((Digtal >> i) & 0x01) {
            printf("●");  // 检测到黑线
        } else {
            printf("○");  // 检测到白色
        }
    }
    printf(" | 位置误差: %.2f\n", g_line_position_error);
}
```

---

## ⚠️ **注意事项**

### **硬件注意事项**
1. **电源电压**: 支持3.3V和5V，建议使用5V以获得更好的检测距离
2. **接线顺序**: 严格按照SCL(PA8)、SDA(PC9)接线，避免短路
3. **上拉电阻**: I2C总线需要上拉电阻，传感器模块通常已集成
4. **安装高度**: 传感器距离地面2-10mm为最佳检测距离

### **软件注意事项**
1. **数据取反**: `Digtal = ~IIC_Get_Digtal()` 是必要的，因为传感器输出逻辑与预期相反
2. **权重调整**: 根据实际车辆宽度和传感器间距调整权重值
3. **滤波处理**: 在高速运行时建议添加数字滤波算法
4. **错误处理**: 建议添加I2C通信错误检测和重试机制

### **调试建议**
1. **连接测试**: 先使用`Ping()`函数确认传感器连接正常
2. **数据监控**: 通过串口输出传感器原始数据进行调试
3. **权重优化**: 根据实际循迹效果调整权重参数
4. **PID调参**: 结合传感器特性调整循迹PID参数

---

## 📊 **传感器数据格式**

### **数字模式数据**
```
位7  位6  位5  位4  位3  位2  位1  位0
[8] [7] [6] [5] [4] [3] [2] [1]
 ↑   ↑   ↑   ↑   ↑   ↑   ↑   ↑
右  →  →  →  →  →  →  → 左

1 = 检测到黑线
0 = 检测到白色/背景
```

### **位置误差计算**
```
位置误差 = Σ(权重 × 传感器状态) / 检测到的传感器数量

示例:
- 传感器3,4检测到黑线: (-2 + -1) / 2 = -1.5 (偏左)
- 传感器5,6检测到黑线: (1 + 2) / 2 = 1.5 (偏右)
- 传感器4,5检测到黑线: (-1 + 1) / 2 = 0 (居中)
```

---

**[文档版本]**: v1.0  
**[更新时间]**: 2025-01-29  
**[适用硬件]**: STM32F407VET6 + 感为智能8路灰度传感器  
**[通信协议]**: I2C3 (PA8-SCL, PC9-SDA)