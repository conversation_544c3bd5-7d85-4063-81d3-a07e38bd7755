--cpu=Cortex-M4.fp.sp
"c30d\startup_stm32f407xx.o"
"c30d\main.o"
"c30d\gpio.o"
"c30d\adc.o"
"c30d\can.o"
"c30d\i2c.o"
"c30d\tim.o"
"c30d\usart.o"
"c30d\stm32f4xx_it.o"
"c30d\stm32f4xx_hal_msp.o"
"c30d\stm32f4xx_hal_adc.o"
"c30d\stm32f4xx_hal_adc_ex.o"
"c30d\stm32f4xx_ll_adc.o"
"c30d\stm32f4xx_hal_rcc.o"
"c30d\stm32f4xx_hal_rcc_ex.o"
"c30d\stm32f4xx_hal_flash.o"
"c30d\stm32f4xx_hal_flash_ex.o"
"c30d\stm32f4xx_hal_flash_ramfunc.o"
"c30d\stm32f4xx_hal_gpio.o"
"c30d\stm32f4xx_hal_dma_ex.o"
"c30d\stm32f4xx_hal_dma.o"
"c30d\stm32f4xx_hal_pwr.o"
"c30d\stm32f4xx_hal_pwr_ex.o"
"c30d\stm32f4xx_hal_cortex.o"
"c30d\stm32f4xx_hal.o"
"c30d\stm32f4xx_hal_exti.o"
"c30d\stm32f4xx_hal_can.o"
"c30d\stm32f4xx_hal_i2c.o"
"c30d\stm32f4xx_hal_i2c_ex.o"
"c30d\stm32f4xx_hal_tim.o"
"c30d\stm32f4xx_hal_tim_ex.o"
"c30d\stm32f4xx_hal_uart.o"
"c30d\system_stm32f4xx.o"
--strict --scatter "C30D\C30D.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "C30D.map" -o C30D\C30D.axf