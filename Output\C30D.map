Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.CAN1_RX0_IRQHandler) for CAN1_RX0_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.TIM3_IRQHandler) for TIM3_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.TIM4_IRQHandler) for TIM4_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.TIM8_CC_IRQHandler) for TIM8_CC_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.TIM5_IRQHandler) for TIM5_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UART5_IRQHandler) for UART5_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.TIM7_IRQHandler) for TIM7_IRQHandler
    startup_stm32f407xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(HEAP) for Heap_Mem
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(STACK) for Stack_Mem
    main.o(i.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to adc.o(i.MX_ADC1_Init) for MX_ADC1_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to can.o(i.MX_CAN1_Init) for MX_CAN1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM1_Init) for MX_TIM1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM2_Init) for MX_TIM2_Init
    main.o(i.main) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    main.o(i.main) refers to tim.o(i.MX_TIM4_Init) for MX_TIM4_Init
    main.o(i.main) refers to tim.o(i.MX_TIM5_Init) for MX_TIM5_Init
    main.o(i.main) refers to tim.o(i.MX_TIM8_Init) for MX_TIM8_Init
    main.o(i.main) refers to tim.o(i.MX_TIM9_Init) for MX_TIM9_Init
    main.o(i.main) refers to tim.o(i.MX_TIM10_Init) for MX_TIM10_Init
    main.o(i.main) refers to tim.o(i.MX_TIM11_Init) for MX_TIM11_Init
    main.o(i.main) refers to tim.o(i.MX_TIM12_Init) for MX_TIM12_Init
    main.o(i.main) refers to usart.o(i.MX_UART5_Init) for MX_UART5_Init
    main.o(i.main) refers to usart.o(i.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART3_UART_Init) for MX_USART3_UART_Init
    main.o(i.main) refers to delay.o(i.delay_init) for delay_init
    main.o(i.main) refers to mpu6050.o(i.MPU6050_initialize) for MPU6050_initialize
    main.o(i.main) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.main) refers to robot_select_init.o(i.Robot_Select) for Robot_Select
    main.o(i.main) refers to pstwo.o(i.PS2_SetInit) for PS2_SetInit
    main.o(i.main) refers to tim.o(i.MX_TIM7_Init) for MX_TIM7_Init
    main.o(i.main) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    main.o(i.main) refers to show.o(i.show_task) for show_task
    main.o(i.main) refers to pstwo.o(i.PS2_Read) for PS2_Read
    main.o(i.main) refers to usartx.o(i.data_task) for data_task
    main.o(i.main) refers to tim.o(.bss) for htim7
    main.o(i.main) refers to usartx.o(.data) for data_sent_flag
    gpio.o(i.MX_GPIO_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.Get_Adc) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.Get_Adc) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Start) for HAL_ADC_Start
    adc.o(i.Get_Adc) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion) for HAL_ADC_PollForConversion
    adc.o(i.Get_Adc) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_GetState) for HAL_ADC_GetState
    adc.o(i.Get_Adc) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_GetValue) for HAL_ADC_GetValue
    adc.o(i.Get_Adc) refers to adc.o(.bss) for .bss
    adc.o(i.Get_adc_Average) refers to adc.o(i.Get_Adc) for Get_Adc
    adc.o(i.Get_adc_Average) refers to delay.o(i.delay_ms) for delay_ms
    adc.o(i.Get_battery_volt) refers to adc.o(i.Get_Adc) for Get_Adc
    adc.o(i.Get_battery_volt) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    adc.o(i.Get_battery_volt) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    adc.o(i.Get_battery_volt) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    adc.o(i.HAL_ADC_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.MX_ADC1_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.MX_ADC1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.MX_ADC1_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.MX_ADC1_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Start) for HAL_ADC_Start
    adc.o(i.MX_ADC1_Init) refers to adc.o(.bss) for .bss
    can.o(i.CAN1_Receive_Msg) refers to can.o(i.CAN1_Msg_Pend) for CAN1_Msg_Pend
    can.o(i.CAN1_Receive_Msg) refers to can.o(i.CAN1_Rx_Msg) for CAN1_Rx_Msg
    can.o(i.CAN1_Send_Msg) refers to can.o(i.CAN1_Tx_Msg) for CAN1_Tx_Msg
    can.o(i.CAN1_Send_Msg) refers to can.o(i.CAN1_Tx_Staus) for CAN1_Tx_Staus
    can.o(i.CAN1_Send_MsgTEST) refers to can.o(i.CAN1_Tx_Msg) for CAN1_Tx_Msg
    can.o(i.CAN1_Send_MsgTEST) refers to can.o(i.CAN1_Tx_Staus) for CAN1_Tx_Staus
    can.o(i.CAN1_Send_Num) refers to can.o(i.CAN1_Tx_Msg) for CAN1_Tx_Msg
    can.o(i.CAN1_Send_Num) refers to can.o(i.CAN1_Tx_Staus) for CAN1_Tx_Staus
    can.o(i.HAL_CAN_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    can.o(i.HAL_CAN_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    can.o(i.HAL_CAN_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    can.o(i.HAL_CAN_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    can.o(i.HAL_CAN_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    can.o(i.HAL_CAN_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    can.o(i.MX_CAN1_Init) refers to stm32f4xx_hal_can.o(i.HAL_CAN_Init) for HAL_CAN_Init
    can.o(i.MX_CAN1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    can.o(i.MX_CAN1_Init) refers to can.o(.bss) for .bss
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(i.HAL_I2C_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.I2C_Ack) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.I2C_NAck) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.I2C_ReadBuff) refers to i2c.o(i.I2C_Start) for I2C_Start
    i2c.o(i.I2C_ReadBuff) refers to i2c.o(i.I2C_WriteByte) for I2C_WriteByte
    i2c.o(i.I2C_ReadBuff) refers to i2c.o(i.I2C_WaiteForAck) for I2C_WaiteForAck
    i2c.o(i.I2C_ReadBuff) refers to i2c.o(i.I2C_ReadByte) for I2C_ReadByte
    i2c.o(i.I2C_ReadBuff) refers to i2c.o(i.I2C_Stop) for I2C_Stop
    i2c.o(i.I2C_ReadByte) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.I2C_ReadByte) refers to i2c.o(i.I2C_NAck) for I2C_NAck
    i2c.o(i.I2C_ReadByte) refers to i2c.o(i.I2C_Ack) for I2C_Ack
    i2c.o(i.I2C_ReadOneByte) refers to i2c.o(i.I2C_Start) for I2C_Start
    i2c.o(i.I2C_ReadOneByte) refers to i2c.o(i.I2C_WriteByte) for I2C_WriteByte
    i2c.o(i.I2C_ReadOneByte) refers to i2c.o(i.I2C_WaiteForAck) for I2C_WaiteForAck
    i2c.o(i.I2C_ReadOneByte) refers to i2c.o(i.I2C_ReadByte) for I2C_ReadByte
    i2c.o(i.I2C_ReadOneByte) refers to i2c.o(i.I2C_Stop) for I2C_Stop
    i2c.o(i.I2C_Start) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.I2C_Stop) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.I2C_WaiteForAck) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.I2C_WaiteForAck) refers to i2c.o(i.I2C_Stop) for I2C_Stop
    i2c.o(i.I2C_WriteBits) refers to i2c.o(i.I2C_ReadOneByte) for I2C_ReadOneByte
    i2c.o(i.I2C_WriteBits) refers to i2c.o(i.I2C_WriteOneByte) for I2C_WriteOneByte
    i2c.o(i.I2C_WriteBuff) refers to i2c.o(i.I2C_Start) for I2C_Start
    i2c.o(i.I2C_WriteBuff) refers to i2c.o(i.I2C_WriteByte) for I2C_WriteByte
    i2c.o(i.I2C_WriteBuff) refers to i2c.o(i.I2C_WaiteForAck) for I2C_WaiteForAck
    i2c.o(i.I2C_WriteBuff) refers to i2c.o(i.I2C_Stop) for I2C_Stop
    i2c.o(i.I2C_WriteByte) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.I2C_WriteOneBit) refers to i2c.o(i.I2C_ReadOneByte) for I2C_ReadOneByte
    i2c.o(i.I2C_WriteOneBit) refers to i2c.o(i.I2C_WriteOneByte) for I2C_WriteOneByte
    i2c.o(i.I2C_WriteOneByte) refers to i2c.o(i.I2C_Start) for I2C_Start
    i2c.o(i.I2C_WriteOneByte) refers to i2c.o(i.I2C_WriteByte) for I2C_WriteByte
    i2c.o(i.I2C_WriteOneByte) refers to i2c.o(i.I2C_WaiteForAck) for I2C_WaiteForAck
    i2c.o(i.I2C_WriteOneByte) refers to i2c.o(i.I2C_Stop) for I2C_Stop
    i2c.o(i.MX_I2C2_Init) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C2_Init) refers to i2c.o(.bss) for .bss
    tim.o(i.HAL_TIM_Base_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.HAL_TIM_Encoder_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(i.HAL_TIM_Encoder_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.HAL_TIM_IC_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(i.HAL_TIM_IC_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    tim.o(i.HAL_TIM_IC_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_IC_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.HAL_TIM_IC_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    tim.o(i.HAL_TIM_IC_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.HAL_TIM_MspPostInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_MspPostInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.MX_TIM10_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM10_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM10_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM10_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM10_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM10_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    tim.o(i.MX_TIM10_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM10_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM11_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM11_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM11_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM11_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM11_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM11_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    tim.o(i.MX_TIM11_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM11_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM12_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM12_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM12_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM12_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM12_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    tim.o(i.MX_TIM12_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM12_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM1_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime) for HAL_TIMEx_ConfigBreakDeadTime
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    tim.o(i.MX_TIM1_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM1_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM2_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) for HAL_TIM_Encoder_Start
    tim.o(i.MX_TIM2_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM3_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) for HAL_TIM_Encoder_Start
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM4_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM4_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) for HAL_TIM_Encoder_Start
    tim.o(i.MX_TIM4_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM5_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM5_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM5_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM5_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM5_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) for HAL_TIM_Encoder_Start
    tim.o(i.MX_TIM5_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM7_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM7_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM7_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM7_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM8_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) for HAL_TIM_IC_Init
    tim.o(i.MX_TIM8_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM8_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM8_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) for HAL_TIM_IC_ConfigChannel
    tim.o(i.MX_TIM8_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM8_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    tim.o(i.MX_TIM8_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM9_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM9_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM9_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM9_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM9_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    tim.o(i.MX_TIM9_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM9_Init) refers to tim.o(.bss) for .bss
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.MX_UART5_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_UART5_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_UART5_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    usart.o(i.MX_UART5_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_UART5_Init) refers to usartx.o(.data) for Uart5_Receive_buf
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to usartx.o(.data) for Usart1_Receive_buf
    usart.o(i.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART2_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    usart.o(i.MX_USART2_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART2_UART_Init) refers to usartx.o(.data) for Usart2_Receive_buf
    usart.o(i.MX_USART3_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART3_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART3_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    usart.o(i.MX_USART3_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART3_UART_Init) refers to usartx.o(.data) for Usart3_Receive_buf
    usart.o(i.fputc) refers to usart.o(.data) for .data
    stm32f4xx_it.o(i.CAN1_RX0_IRQHandler) refers to stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler) for HAL_CAN_IRQHandler
    stm32f4xx_it.o(i.CAN1_RX0_IRQHandler) refers to can.o(.bss) for hcan1
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(i.TIM2_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f4xx_it.o(i.TIM2_IRQHandler) refers to tim.o(.bss) for htim2
    stm32f4xx_it.o(i.TIM3_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f4xx_it.o(i.TIM3_IRQHandler) refers to tim.o(.bss) for htim3
    stm32f4xx_it.o(i.TIM4_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f4xx_it.o(i.TIM4_IRQHandler) refers to tim.o(.bss) for htim4
    stm32f4xx_it.o(i.TIM5_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f4xx_it.o(i.TIM5_IRQHandler) refers to tim.o(.bss) for htim5
    stm32f4xx_it.o(i.TIM7_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f4xx_it.o(i.TIM7_IRQHandler) refers to tim.o(.bss) for htim7
    stm32f4xx_it.o(i.TIM8_CC_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f4xx_it.o(i.TIM8_CC_IRQHandler) refers to tim.o(.bss) for htim8
    stm32f4xx_it.o(i.UART5_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.UART5_IRQHandler) refers to usart.o(.bss) for huart5
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to usart.o(.bss) for huart2
    stm32f4xx_it.o(i.USART3_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART3_IRQHandler) refers to usart.o(.bss) for huart3
    stm32f4xx_hal_msp.o(i.HAL_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAError) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to adc.o(i.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback) for HAL_ADCEx_InjectedConvCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_Init) refers to adc.o(i.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32f4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32f4xx_hal_adc.o(i.ADC_Init) for ADC_Init
    stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(i.HAL_ADC_PollForEvent) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt) for ADC_MultiModeDMAConvCplt
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt) for ADC_MultiModeDMAHalfConvCplt
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError) for ADC_MultiModeDMAError
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for .constdata
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal_can.o(i.HAL_CAN_DeInit) refers to stm32f4xx_hal_can.o(i.HAL_CAN_Stop) for HAL_CAN_Stop
    stm32f4xx_hal_can.o(i.HAL_CAN_DeInit) refers to can.o(i.HAL_CAN_MspDeInit) for HAL_CAN_MspDeInit
    stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox0CompleteCallback) for HAL_CAN_TxMailbox0CompleteCallback
    stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox0AbortCallback) for HAL_CAN_TxMailbox0AbortCallback
    stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox1CompleteCallback) for HAL_CAN_TxMailbox1CompleteCallback
    stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox1AbortCallback) for HAL_CAN_TxMailbox1AbortCallback
    stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox2CompleteCallback) for HAL_CAN_TxMailbox2CompleteCallback
    stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox2AbortCallback) for HAL_CAN_TxMailbox2AbortCallback
    stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(i.HAL_CAN_RxFifo0FullCallback) for HAL_CAN_RxFifo0FullCallback
    stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(i.HAL_CAN_RxFifo0MsgPendingCallback) for HAL_CAN_RxFifo0MsgPendingCallback
    stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(i.HAL_CAN_RxFifo1FullCallback) for HAL_CAN_RxFifo1FullCallback
    stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(i.HAL_CAN_RxFifo1MsgPendingCallback) for HAL_CAN_RxFifo1MsgPendingCallback
    stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(i.HAL_CAN_SleepCallback) for HAL_CAN_SleepCallback
    stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(i.HAL_CAN_WakeUpFromRxMsgCallback) for HAL_CAN_WakeUpFromRxMsgCallback
    stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(i.HAL_CAN_ErrorCallback) for HAL_CAN_ErrorCallback
    stm32f4xx_hal_can.o(i.HAL_CAN_Init) refers to can.o(i.HAL_CAN_MspInit) for HAL_CAN_MspInit
    stm32f4xx_hal_can.o(i.HAL_CAN_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_can.o(i.HAL_CAN_Start) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_can.o(i.HAL_CAN_Stop) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) for I2C_Slave_AF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) for I2C_Slave_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_SB) for I2C_Master_SB
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR) for I2C_Master_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) for I2C_Slave_STOPF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) for I2C_MasterRequestRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) for I2C_MasterRequestWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to balance.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAError) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to balance.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to usartx.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to usartx.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for .constdata
    datascope_dp.o(i.DataScope_Data_Generate) refers to datascope_dp.o(.bss) for .bss
    datascope_dp.o(i.DataScope_Get_Channel_Data) refers to datascope_dp.o(i.Float2Byte) for Float2Byte
    datascope_dp.o(i.DataScope_Get_Channel_Data) refers to datascope_dp.o(.bss) for .bss
    key.o(i.Long_Press) refers to key.o(.data) for .data
    key.o(i.click) refers to key.o(.data) for .data
    key.o(i.click_N_Double) refers to key.o(.data) for .data
    key.o(i.click_N_Double_MPU6050) refers to key.o(.data) for .data
    led.o(i.Buzzer_Alarm) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    led.o(i.Led_Flash) refers to led.o(.data) for .data
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Refresh_Gram) for OLED_Refresh_Gram
    oled.o(i.OLED_Clear) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_Display_Off) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Display_On) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DrawPoint) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_Init) refers to delay.o(i.delay_ms) for delay_ms
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Refresh_Gram) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Refresh_Gram) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_Set_Pos) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_ShowCHinese) refers to oled.o(i.OLED_Set_Pos) for OLED_Set_Pos
    oled.o(i.OLED_ShowCHinese) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_ShowCHinese) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_ShowNumber) refers to oled.o(i.oled_pow) for oled_pow
    oled.o(i.OLED_ShowNumber) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    pstwo.o(i.PS2_AnologData) refers to pstwo.o(.bss) for .bss
    pstwo.o(i.PS2_ClearData) refers to pstwo.o(.bss) for .bss
    pstwo.o(i.PS2_Cmd) refers to delay.o(i.delay_us) for delay_us
    pstwo.o(i.PS2_Cmd) refers to pstwo.o(.bss) for .bss
    pstwo.o(i.PS2_DataKey) refers to pstwo.o(i.PS2_ClearData) for PS2_ClearData
    pstwo.o(i.PS2_DataKey) refers to pstwo.o(i.PS2_ReadData) for PS2_ReadData
    pstwo.o(i.PS2_DataKey) refers to pstwo.o(.bss) for .bss
    pstwo.o(i.PS2_DataKey) refers to pstwo.o(.data) for .data
    pstwo.o(i.PS2_EnterConfing) refers to delay.o(i.delay_us) for delay_us
    pstwo.o(i.PS2_EnterConfing) refers to pstwo.o(i.PS2_Cmd) for PS2_Cmd
    pstwo.o(i.PS2_ExitConfing) refers to delay.o(i.delay_us) for delay_us
    pstwo.o(i.PS2_ExitConfing) refers to pstwo.o(i.PS2_Cmd) for PS2_Cmd
    pstwo.o(i.PS2_Read) refers to pstwo.o(i.PS2_DataKey) for PS2_DataKey
    pstwo.o(i.PS2_Read) refers to system.o(.data) for PS2_KEY
    pstwo.o(i.PS2_Read) refers to pstwo.o(.bss) for .bss
    pstwo.o(i.PS2_Read) refers to system.o(.data) for PS2_LX
    pstwo.o(i.PS2_Read) refers to system.o(.data) for PS2_LY
    pstwo.o(i.PS2_Read) refers to system.o(.data) for PS2_RX
    pstwo.o(i.PS2_Read) refers to system.o(.data) for PS2_RY
    pstwo.o(i.PS2_Read) refers to pstwo.o(.data) for .data
    pstwo.o(i.PS2_Read) refers to system.o(.data) for PS2_ON_Flag
    pstwo.o(i.PS2_Read) refers to system.o(.data) for Remote_ON_Flag
    pstwo.o(i.PS2_Read) refers to system.o(.data) for APP_ON_Flag
    pstwo.o(i.PS2_Read) refers to system.o(.data) for CAN_ON_Flag
    pstwo.o(i.PS2_Read) refers to system.o(.data) for Usart1_ON_Flag
    pstwo.o(i.PS2_Read) refers to system.o(.data) for Usart5_ON_Flag
    pstwo.o(i.PS2_ReadData) refers to pstwo.o(i.PS2_Cmd) for PS2_Cmd
    pstwo.o(i.PS2_ReadData) refers to delay.o(i.delay_us) for delay_us
    pstwo.o(i.PS2_ReadData) refers to pstwo.o(.data) for .data
    pstwo.o(i.PS2_ReadData) refers to pstwo.o(.bss) for .bss
    pstwo.o(i.PS2_Receive) refers to pstwo.o(i.PS2_DataKey) for PS2_DataKey
    pstwo.o(i.PS2_Receive) refers to system.o(.data) for PS2_ON_Flag
    pstwo.o(i.PS2_Receive) refers to pstwo.o(.bss) for .bss
    pstwo.o(i.PS2_Receive) refers to system.o(.data) for PS2_LX
    pstwo.o(i.PS2_Receive) refers to system.o(.data) for PS2_LY
    pstwo.o(i.PS2_Receive) refers to system.o(.data) for PS2_RX
    pstwo.o(i.PS2_Receive) refers to system.o(.data) for PS2_RY
    pstwo.o(i.PS2_Receive) refers to system.o(.data) for PS2_KEY
    pstwo.o(i.PS2_RedLight) refers to pstwo.o(i.PS2_Cmd) for PS2_Cmd
    pstwo.o(i.PS2_RedLight) refers to pstwo.o(.data) for .data
    pstwo.o(i.PS2_RedLight) refers to pstwo.o(.bss) for .bss
    pstwo.o(i.PS2_SetInit) refers to pstwo.o(i.PS2_ShortPoll) for PS2_ShortPoll
    pstwo.o(i.PS2_SetInit) refers to pstwo.o(i.PS2_EnterConfing) for PS2_EnterConfing
    pstwo.o(i.PS2_SetInit) refers to pstwo.o(i.PS2_TurnOnAnalogMode) for PS2_TurnOnAnalogMode
    pstwo.o(i.PS2_SetInit) refers to pstwo.o(i.PS2_ExitConfing) for PS2_ExitConfing
    pstwo.o(i.PS2_ShortPoll) refers to delay.o(i.delay_us) for delay_us
    pstwo.o(i.PS2_ShortPoll) refers to pstwo.o(i.PS2_Cmd) for PS2_Cmd
    pstwo.o(i.PS2_TurnOnAnalogMode) refers to pstwo.o(i.PS2_Cmd) for PS2_Cmd
    pstwo.o(i.PS2_TurnOnAnalogMode) refers to delay.o(i.delay_us) for delay_us
    pstwo.o(i.PS2_Vibration) refers to delay.o(i.delay_us) for delay_us
    pstwo.o(i.PS2_Vibration) refers to pstwo.o(i.PS2_Cmd) for PS2_Cmd
    pstwo.o(i.PS2_VibrationMode) refers to delay.o(i.delay_us) for delay_us
    pstwo.o(i.PS2_VibrationMode) refers to pstwo.o(i.PS2_Cmd) for PS2_Cmd
    usartx.o(i.CAN_SEND) refers to can.o(i.CAN1_Send_Num) for CAN1_Send_Num
    usartx.o(i.CAN_SEND) refers to usartx.o(.bss) for .bss
    usartx.o(i.Check_Sum) refers to usartx.o(.bss) for .bss
    usartx.o(i.HAL_UART_RxCpltCallback) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    usartx.o(i.HAL_UART_RxCpltCallback) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    usartx.o(i.HAL_UART_RxCpltCallback) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    usartx.o(i.HAL_UART_RxCpltCallback) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    usartx.o(i.HAL_UART_RxCpltCallback) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    usartx.o(i.HAL_UART_RxCpltCallback) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    usartx.o(i.HAL_UART_RxCpltCallback) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usartx.o(i.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    usartx.o(i.HAL_UART_RxCpltCallback) refers to usartx.o(.data) for .data
    usartx.o(i.HAL_UART_RxCpltCallback) refers to system.o(.data) for APP_ON_Flag
    usartx.o(i.HAL_UART_RxCpltCallback) refers to system.o(.data) for PS2_ON_Flag
    usartx.o(i.HAL_UART_RxCpltCallback) refers to system.o(.data) for Remote_ON_Flag
    usartx.o(i.HAL_UART_RxCpltCallback) refers to system.o(.data) for CAN_ON_Flag
    usartx.o(i.HAL_UART_RxCpltCallback) refers to system.o(.data) for Usart1_ON_Flag
    usartx.o(i.HAL_UART_RxCpltCallback) refers to system.o(.data) for Usart5_ON_Flag
    usartx.o(i.HAL_UART_RxCpltCallback) refers to system.o(.data) for Flag_Direction
    usartx.o(i.HAL_UART_RxCpltCallback) refers to system.o(.data) for Flag_Left
    usartx.o(i.HAL_UART_RxCpltCallback) refers to system.o(.data) for Flag_Right
    usartx.o(i.HAL_UART_RxCpltCallback) refers to system.o(.data) for Turn_Flag
    usartx.o(i.HAL_UART_RxCpltCallback) refers to usartx.o(.bss) for .bss
    usartx.o(i.HAL_UART_RxCpltCallback) refers to system.o(.data) for RC_Velocity
    usartx.o(i.HAL_UART_RxCpltCallback) refers to system.o(.data) for PID_Send
    usartx.o(i.HAL_UART_RxCpltCallback) refers to system.o(.data) for Velocity_KP
    usartx.o(i.HAL_UART_RxCpltCallback) refers to system.o(.data) for Velocity_KI
    usartx.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.bss) for huart2
    usartx.o(i.USART1_SEND) refers to usartx.o(i.usart1_send) for usart1_send
    usartx.o(i.USART1_SEND) refers to usartx.o(.bss) for .bss
    usartx.o(i.USART3_SEND) refers to usartx.o(i.usart3_send) for usart3_send
    usartx.o(i.USART3_SEND) refers to usartx.o(.bss) for .bss
    usartx.o(i.USART5_SEND) refers to usartx.o(i.usart5_send) for usart5_send
    usartx.o(i.USART5_SEND) refers to usartx.o(.bss) for .bss
    usartx.o(i.Vz_to_Akm_Angle) refers to balance.o(i.float_abs) for float_abs
    usartx.o(i.Vz_to_Akm_Angle) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    usartx.o(i.Vz_to_Akm_Angle) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    usartx.o(i.Vz_to_Akm_Angle) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    usartx.o(i.Vz_to_Akm_Angle) refers to system.o(.data) for Wheel_spacing
    usartx.o(i.Vz_to_Akm_Angle) refers to system.o(.data) for Axle_spacing
    usartx.o(i.XYZ_Target_Speed_transition) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    usartx.o(i.XYZ_Target_Speed_transition) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    usartx.o(i.XYZ_Target_Speed_transition) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    usartx.o(i.XYZ_Target_Speed_transition) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    usartx.o(i.data_task) refers to usartx.o(i.data_transition) for data_transition
    usartx.o(i.data_task) refers to usartx.o(i.USART1_SEND) for USART1_SEND
    usartx.o(i.data_task) refers to usartx.o(i.USART3_SEND) for USART3_SEND
    usartx.o(i.data_task) refers to usartx.o(i.USART5_SEND) for USART5_SEND
    usartx.o(i.data_task) refers to usartx.o(i.CAN_SEND) for CAN_SEND
    usartx.o(i.data_task) refers to usartx.o(.data) for .data
    usartx.o(i.data_transition) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    usartx.o(i.data_transition) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    usartx.o(i.data_transition) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    usartx.o(i.data_transition) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    usartx.o(i.data_transition) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    usartx.o(i.data_transition) refers to usartx.o(i.Check_Sum) for Check_Sum
    usartx.o(i.data_transition) refers to usartx.o(.bss) for .bss
    usartx.o(i.data_transition) refers to system.o(.data) for Car_Mode
    usartx.o(i.data_transition) refers to system.o(.bss) for MOTOR_A
    usartx.o(i.data_transition) refers to system.o(.bss) for MOTOR_B
    usartx.o(i.data_transition) refers to system.o(.bss) for MOTOR_C
    usartx.o(i.data_transition) refers to system.o(.bss) for MOTOR_D
    usartx.o(i.data_transition) refers to system.o(.data) for Axle_spacing
    usartx.o(i.data_transition) refers to system.o(.data) for Wheel_spacing
    usartx.o(i.data_transition) refers to system.o(.data) for Omni_turn_radiaus
    usartx.o(i.data_transition) refers to mpu6050.o(.data) for accel
    usartx.o(i.data_transition) refers to system.o(.data) for Flag_Stop
    usartx.o(i.data_transition) refers to adc.o(.data) for Voltage
    balance.o(i.Drive_Motor) refers to balance.o(i.Smooth_control) for Smooth_control
    balance.o(i.Drive_Motor) refers to balance.o(i.target_limit_float) for target_limit_float
    balance.o(i.Drive_Motor) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    balance.o(i.Drive_Motor) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    balance.o(i.Drive_Motor) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    balance.o(i.Drive_Motor) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    balance.o(i.Drive_Motor) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    balance.o(i.Drive_Motor) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    balance.o(i.Drive_Motor) refers to tan.o(i.__hardfp_tan) for __hardfp_tan
    balance.o(i.Drive_Motor) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    balance.o(i.Drive_Motor) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    balance.o(i.Drive_Motor) refers to balance.o(i.target_limit_int) for target_limit_int
    balance.o(i.Drive_Motor) refers to system.o(.data) for Car_Mode
    balance.o(i.Drive_Motor) refers to system.o(.data) for Axle_spacing
    balance.o(i.Drive_Motor) refers to system.o(.bss) for MOTOR_C
    balance.o(i.Drive_Motor) refers to system.o(.bss) for MOTOR_D
    balance.o(i.Drive_Motor) refers to system.o(.data) for Wheel_spacing
    balance.o(i.Drive_Motor) refers to system.o(.bss) for MOTOR_A
    balance.o(i.Drive_Motor) refers to system.o(.bss) for MOTOR_B
    balance.o(i.Drive_Motor) refers to system.o(.bss) for smooth_control
    balance.o(i.Drive_Motor) refers to system.o(.data) for Omni_turn_radiaus
    balance.o(i.Drive_Motor) refers to system.o(.data) for Servo
    balance.o(i.Get_RC) refers to balance.o(i.Drive_Motor) for Drive_Motor
    balance.o(i.Get_RC) refers to system.o(.data) for Car_Mode
    balance.o(i.Get_RC) refers to system.o(.data) for Flag_Left
    balance.o(i.Get_RC) refers to system.o(.data) for Flag_Right
    balance.o(i.Get_RC) refers to system.o(.data) for Flag_Direction
    balance.o(i.Get_RC) refers to system.o(.data) for Move_Y
    balance.o(i.Get_RC) refers to system.o(.data) for RC_Velocity
    balance.o(i.Get_RC) refers to system.o(.data) for Move_X
    balance.o(i.Get_RC) refers to system.o(.data) for Move_Z
    balance.o(i.Get_Velocity_Form_Encoder) refers to encoder.o(i.Read_Encoder) for Read_Encoder
    balance.o(i.Get_Velocity_Form_Encoder) refers to balance.o(.bss) for .bss
    balance.o(i.Get_Velocity_Form_Encoder) refers to system.o(.data) for Car_Mode
    balance.o(i.Get_Velocity_Form_Encoder) refers to system.o(.data) for Wheel_perimeter
    balance.o(i.Get_Velocity_Form_Encoder) refers to system.o(.data) for Encoder_precision
    balance.o(i.Get_Velocity_Form_Encoder) refers to system.o(.bss) for MOTOR_A
    balance.o(i.Get_Velocity_Form_Encoder) refers to system.o(.bss) for MOTOR_B
    balance.o(i.Get_Velocity_Form_Encoder) refers to system.o(.bss) for MOTOR_C
    balance.o(i.Get_Velocity_Form_Encoder) refers to system.o(.bss) for MOTOR_D
    balance.o(i.HAL_TIM_PeriodElapsedCallback) refers to balance.o(i.Get_Velocity_Form_Encoder) for Get_Velocity_Form_Encoder
    balance.o(i.HAL_TIM_PeriodElapsedCallback) refers to balance.o(i.Get_RC) for Get_RC
    balance.o(i.HAL_TIM_PeriodElapsedCallback) refers to balance.o(i.Remote_Control) for Remote_Control
    balance.o(i.HAL_TIM_PeriodElapsedCallback) refers to balance.o(i.PS2_control) for PS2_control
    balance.o(i.HAL_TIM_PeriodElapsedCallback) refers to balance.o(i.Drive_Motor) for Drive_Motor
    balance.o(i.HAL_TIM_PeriodElapsedCallback) refers to balance.o(i.Key) for Key
    balance.o(i.HAL_TIM_PeriodElapsedCallback) refers to balance.o(i.Turn_Off) for Turn_Off
    balance.o(i.HAL_TIM_PeriodElapsedCallback) refers to balance.o(i.Incremental_PI_A) for Incremental_PI_A
    balance.o(i.HAL_TIM_PeriodElapsedCallback) refers to balance.o(i.Incremental_PI_B) for Incremental_PI_B
    balance.o(i.HAL_TIM_PeriodElapsedCallback) refers to balance.o(i.Incremental_PI_C) for Incremental_PI_C
    balance.o(i.HAL_TIM_PeriodElapsedCallback) refers to balance.o(i.Incremental_PI_D) for Incremental_PI_D
    balance.o(i.HAL_TIM_PeriodElapsedCallback) refers to balance.o(i.Limit_Pwm) for Limit_Pwm
    balance.o(i.HAL_TIM_PeriodElapsedCallback) refers to balance.o(i.Set_Pwm) for Set_Pwm
    balance.o(i.HAL_TIM_PeriodElapsedCallback) refers to mpu6050.o(i.MPU6050_task) for MPU6050_task
    balance.o(i.HAL_TIM_PeriodElapsedCallback) refers to led.o(i.Led_Flash) for Led_Flash
    balance.o(i.HAL_TIM_PeriodElapsedCallback) refers to system.o(.data) for Check
    balance.o(i.HAL_TIM_PeriodElapsedCallback) refers to system.o(.data) for APP_ON_Flag
    balance.o(i.HAL_TIM_PeriodElapsedCallback) refers to system.o(.data) for Remote_ON_Flag
    balance.o(i.HAL_TIM_PeriodElapsedCallback) refers to system.o(.data) for PS2_ON_Flag
    balance.o(i.HAL_TIM_PeriodElapsedCallback) refers to system.o(.data) for Move_Z
    balance.o(i.HAL_TIM_PeriodElapsedCallback) refers to system.o(.data) for Move_Y
    balance.o(i.HAL_TIM_PeriodElapsedCallback) refers to system.o(.data) for Move_X
    balance.o(i.HAL_TIM_PeriodElapsedCallback) refers to adc.o(.data) for Voltage
    balance.o(i.HAL_TIM_PeriodElapsedCallback) refers to system.o(.bss) for MOTOR_A
    balance.o(i.HAL_TIM_PeriodElapsedCallback) refers to system.o(.bss) for MOTOR_B
    balance.o(i.HAL_TIM_PeriodElapsedCallback) refers to system.o(.bss) for MOTOR_C
    balance.o(i.HAL_TIM_PeriodElapsedCallback) refers to system.o(.bss) for MOTOR_D
    balance.o(i.HAL_TIM_PeriodElapsedCallback) refers to system.o(.data) for Car_Mode
    balance.o(i.HAL_TIM_PeriodElapsedCallback) refers to balance.o(.data) for .data
    balance.o(i.HAL_TIM_PeriodElapsedCallback) refers to usartx.o(.data) for data_sent_flag
    balance.o(i.HAL_TIM_PeriodElapsedCallback) refers to system.o(.data) for Servo
    balance.o(i.Incremental_PI_A) refers to balance.o(.data) for .data
    balance.o(i.Incremental_PI_A) refers to system.o(.data) for Velocity_KP
    balance.o(i.Incremental_PI_A) refers to system.o(.data) for Velocity_KI
    balance.o(i.Incremental_PI_B) refers to balance.o(.data) for .data
    balance.o(i.Incremental_PI_B) refers to system.o(.data) for Velocity_KP
    balance.o(i.Incremental_PI_B) refers to system.o(.data) for Velocity_KI
    balance.o(i.Incremental_PI_C) refers to balance.o(.data) for .data
    balance.o(i.Incremental_PI_C) refers to system.o(.data) for Velocity_KP
    balance.o(i.Incremental_PI_C) refers to system.o(.data) for Velocity_KI
    balance.o(i.Incremental_PI_D) refers to balance.o(.data) for .data
    balance.o(i.Incremental_PI_D) refers to system.o(.data) for Velocity_KP
    balance.o(i.Incremental_PI_D) refers to system.o(.data) for Velocity_KI
    balance.o(i.Key) refers to key.o(i.click_N_Double_MPU6050) for click_N_Double_MPU6050
    balance.o(i.Key) refers to mpu6050.o(.data) for Deviation_gyro
    balance.o(i.Key) refers to mpu6050.o(.data) for Original_gyro
    balance.o(i.Limit_Pwm) refers to balance.o(i.target_limit_float) for target_limit_float
    balance.o(i.Limit_Pwm) refers to system.o(.bss) for MOTOR_A
    balance.o(i.Limit_Pwm) refers to system.o(.bss) for MOTOR_B
    balance.o(i.Limit_Pwm) refers to system.o(.bss) for MOTOR_C
    balance.o(i.Limit_Pwm) refers to system.o(.bss) for MOTOR_D
    balance.o(i.PS2_control) refers to balance.o(i.Drive_Motor) for Drive_Motor
    balance.o(i.PS2_control) refers to system.o(.data) for PS2_LX
    balance.o(i.PS2_control) refers to system.o(.data) for PS2_LY
    balance.o(i.PS2_control) refers to system.o(.data) for PS2_RX
    balance.o(i.PS2_control) refers to system.o(.data) for PS2_KEY
    balance.o(i.PS2_control) refers to system.o(.data) for RC_Velocity
    balance.o(i.PS2_control) refers to system.o(.data) for Move_X
    balance.o(i.PS2_control) refers to system.o(.data) for Move_Y
    balance.o(i.PS2_control) refers to system.o(.data) for Move_Z
    balance.o(i.PS2_control) refers to system.o(.data) for Car_Mode
    balance.o(i.Remote_Control) refers to balance.o(i.target_limit_int) for target_limit_int
    balance.o(i.Remote_Control) refers to balance.o(i.Drive_Motor) for Drive_Motor
    balance.o(i.Remote_Control) refers to timer.o(.data) for Remoter_Ch1
    balance.o(i.Remote_Control) refers to timer.o(.data) for Remoter_Ch2
    balance.o(i.Remote_Control) refers to timer.o(.data) for Remoter_Ch3
    balance.o(i.Remote_Control) refers to timer.o(.data) for Remoter_Ch4
    balance.o(i.Remote_Control) refers to system.o(.data) for RC_Velocity
    balance.o(i.Remote_Control) refers to system.o(.data) for Move_X
    balance.o(i.Remote_Control) refers to system.o(.data) for Move_Y
    balance.o(i.Remote_Control) refers to system.o(.data) for Move_Z
    balance.o(i.Remote_Control) refers to system.o(.data) for Car_Mode
    balance.o(i.Remote_Control) refers to balance.o(.data) for .data
    balance.o(i.Smooth_control) refers to balance.o(i.float_abs) for float_abs
    balance.o(i.Smooth_control) refers to balance.o(i.target_limit_float) for target_limit_float
    balance.o(i.Smooth_control) refers to system.o(.bss) for smooth_control
    balance.o(i.Turn_Off) refers to system.o(.data) for Flag_Stop
    balance.o(i.robot_mode_check) refers to system.o(.bss) for MOTOR_A
    balance.o(i.robot_mode_check) refers to balance.o(.data) for .data
    balance.o(i.robot_mode_check) refers to system.o(.bss) for MOTOR_B
    balance.o(i.robot_mode_check) refers to system.o(.bss) for MOTOR_C
    balance.o(i.robot_mode_check) refers to system.o(.bss) for MOTOR_D
    balance.o(i.robot_mode_check) refers to system.o(.data) for Flag_Stop
    filter.o(i.Kalman_Filter) refers to filter.o(.data) for .data
    filter.o(i.Kalman_Filter) refers to filter.o(.bss) for .bss
    filter.o(i.Yijielvbo) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    filter.o(i.Yijielvbo) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    filter.o(i.Yijielvbo) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    filter.o(i.Yijielvbo) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    filter.o(i.Yijielvbo) refers to filter.o(.data) for .data
    robot_select_init.o(i.Robot_Init) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    robot_select_init.o(i.Robot_Init) refers to robot_select_init.o(.bss) for .bss
    robot_select_init.o(i.Robot_Init) refers to system.o(.data) for Encoder_precision
    robot_select_init.o(i.Robot_Init) refers to system.o(.data) for Wheel_perimeter
    robot_select_init.o(i.Robot_Init) refers to system.o(.data) for Wheel_spacing
    robot_select_init.o(i.Robot_Init) refers to system.o(.data) for Axle_spacing
    robot_select_init.o(i.Robot_Init) refers to system.o(.data) for Omni_turn_radiaus
    robot_select_init.o(i.Robot_Select) refers to adc.o(i.Get_adc_Average) for Get_adc_Average
    robot_select_init.o(i.Robot_Select) refers to robot_select_init.o(i.Robot_Init) for Robot_Init
    robot_select_init.o(i.Robot_Select) refers to system.o(.data) for Divisor_Mode
    robot_select_init.o(i.Robot_Select) refers to system.o(.data) for Car_Mode
    robot_select_init.o(i.Robot_Select) refers to system.o(.data) for CheckPhrase1
    robot_select_init.o(i.Robot_Select) refers to system.o(.data) for CheckPhrase2
    show.o(i.APP_Show) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    show.o(i.APP_Show) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    show.o(i.APP_Show) refers to _printf_dec.o(.text) for _printf_int_dec
    show.o(i.APP_Show) refers to noretval__2printf.o(.text) for __2printf
    show.o(i.APP_Show) refers to adc.o(.data) for Voltage
    show.o(i.APP_Show) refers to system.o(.bss) for MOTOR_A
    show.o(i.APP_Show) refers to system.o(.bss) for MOTOR_B
    show.o(i.APP_Show) refers to show.o(.data) for .data
    show.o(i.APP_Show) refers to system.o(.data) for PID_Send
    show.o(i.APP_Show) refers to mpu6050.o(.data) for gyro
    show.o(i.APP_Show) refers to system.o(.data) for Velocity_KI
    show.o(i.APP_Show) refers to system.o(.data) for Velocity_KP
    show.o(i.APP_Show) refers to system.o(.data) for RC_Velocity
    show.o(i.oled_show) refers to adc.o(i.Get_adc_Average) for Get_adc_Average
    show.o(i.oled_show) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    show.o(i.oled_show) refers to oled.o(i.OLED_ShowNumber) for OLED_ShowNumber
    show.o(i.oled_show) refers to system.o(.data) for Divisor_Mode
    show.o(i.oled_show) refers to adc.o(.data) for Voltage
    show.o(i.oled_show) refers to show.o(.data) for .data
    show.o(i.oled_show) refers to system.o(.data) for Check
    show.o(i.oled_show) refers to system.o(.data) for Car_Mode
    show.o(i.oled_show) refers to mpu6050.o(.data) for gyro
    show.o(i.oled_show) refers to mpu6050.o(.data) for Deviation_gyro
    show.o(i.oled_show) refers to system.o(.bss) for MOTOR_A
    show.o(i.oled_show) refers to system.o(.bss) for MOTOR_B
    show.o(i.oled_show) refers to system.o(.bss) for MOTOR_C
    show.o(i.oled_show) refers to oled.o(i.OLED_Refresh_Gram) for OLED_Refresh_Gram
    show.o(i.oled_show) refers to system.o(.bss) for MOTOR_D
    show.o(i.oled_show) refers to usartx.o(.bss) for Send_Data
    show.o(i.oled_show) refers to system.o(.data) for Servo
    show.o(i.oled_show) refers to system.o(.data) for PS2_ON_Flag
    show.o(i.oled_show) refers to system.o(.data) for APP_ON_Flag
    show.o(i.oled_show) refers to system.o(.data) for Remote_ON_Flag
    show.o(i.oled_show) refers to system.o(.data) for CAN_ON_Flag
    show.o(i.oled_show) refers to system.o(.data) for Usart1_ON_Flag
    show.o(i.oled_show) refers to system.o(.data) for Usart5_ON_Flag
    show.o(i.oled_show) refers to system.o(.data) for Flag_Stop
    show.o(i.show_task) refers to adc.o(i.Get_battery_volt) for Get_battery_volt
    show.o(i.show_task) refers to show.o(i.APP_Show) for APP_Show
    show.o(i.show_task) refers to show.o(i.oled_show) for oled_show
    show.o(i.show_task) refers to adc.o(.data) for Voltage_All
    show.o(i.show_task) refers to adc.o(.data) for Voltage
    delay.o(i.delay_init) refers to delay.o(.data) for .data
    delay.o(i.delay_ms) refers to delay.o(.data) for .data
    delay.o(i.delay_us) refers to delay.o(.data) for .data
    mpu6050.o(i.MPU6050_Set_LPF) refers to i2c.o(i.I2C_WriteOneByte) for I2C_WriteOneByte
    mpu6050.o(i.MPU6050_Set_Rate) refers to i2c.o(i.I2C_WriteOneByte) for I2C_WriteOneByte
    mpu6050.o(i.MPU6050_Set_Rate) refers to mpu6050.o(i.MPU6050_Set_LPF) for MPU6050_Set_LPF
    mpu6050.o(i.MPU6050_getDeviceID) refers to i2c.o(i.I2C_ReadOneByte) for I2C_ReadOneByte
    mpu6050.o(i.MPU6050_initialize) refers to i2c.o(i.I2C_WriteOneByte) for I2C_WriteOneByte
    mpu6050.o(i.MPU6050_initialize) refers to delay.o(i.delay_ms) for delay_ms
    mpu6050.o(i.MPU6050_initialize) refers to mpu6050.o(i.MPU6050_setFullScaleGyroRange) for MPU6050_setFullScaleGyroRange
    mpu6050.o(i.MPU6050_initialize) refers to mpu6050.o(i.MPU6050_setFullScaleAccelRange) for MPU6050_setFullScaleAccelRange
    mpu6050.o(i.MPU6050_initialize) refers to mpu6050.o(i.MPU6050_Set_Rate) for MPU6050_Set_Rate
    mpu6050.o(i.MPU6050_initialize) refers to i2c.o(i.I2C_ReadOneByte) for I2C_ReadOneByte
    mpu6050.o(i.MPU6050_newValues) refers to mpu6050.o(.bss) for .bss
    mpu6050.o(i.MPU6050_setClockSource) refers to i2c.o(i.I2C_WriteBits) for I2C_WriteBits
    mpu6050.o(i.MPU6050_setFullScaleAccelRange) refers to i2c.o(i.I2C_WriteBits) for I2C_WriteBits
    mpu6050.o(i.MPU6050_setFullScaleGyroRange) refers to i2c.o(i.I2C_WriteBits) for I2C_WriteBits
    mpu6050.o(i.MPU6050_setI2CBypassEnabled) refers to i2c.o(i.I2C_WriteOneBit) for I2C_WriteOneBit
    mpu6050.o(i.MPU6050_setI2CMasterModeEnabled) refers to i2c.o(i.I2C_WriteOneBit) for I2C_WriteOneBit
    mpu6050.o(i.MPU6050_setSleepEnabled) refers to i2c.o(i.I2C_WriteOneBit) for I2C_WriteOneBit
    mpu6050.o(i.MPU6050_task) refers to mpu6050.o(i.MPU_Get_Gyroscope) for MPU_Get_Gyroscope
    mpu6050.o(i.MPU6050_task) refers to mpu6050.o(i.MPU_Get_Accelscope) for MPU_Get_Accelscope
    mpu6050.o(i.MPU6050_testConnection) refers to mpu6050.o(i.MPU6050_getDeviceID) for MPU6050_getDeviceID
    mpu6050.o(i.MPU_Get_Accelscope) refers to i2c.o(i.I2C_ReadOneByte) for I2C_ReadOneByte
    mpu6050.o(i.MPU_Get_Accelscope) refers to mpu6050.o(.data) for .data
    mpu6050.o(i.MPU_Get_Gyroscope) refers to i2c.o(i.I2C_ReadOneByte) for I2C_ReadOneByte
    mpu6050.o(i.MPU_Get_Gyroscope) refers to mpu6050.o(.data) for .data
    mpu6050.o(i.Read_Temperature) refers to i2c.o(i.I2C_ReadOneByte) for I2C_ReadOneByte
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to usart.o(.data) for __stdout
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to usart.o(.data) for __stdout
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    atan.o(i.__hardfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.__hardfp_atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.__hardfp_atan) refers to fabs.o(i.fabs) for fabs
    atan.o(i.__hardfp_atan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan.o(i.__hardfp_atan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan.o(i.__hardfp_atan) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan.o(i.__hardfp_atan) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan.o(i.__hardfp_atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.__hardfp_atan) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan.o(i.__hardfp_atan) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan.o(i.__hardfp_atan) refers to atan.o(.constdata) for .constdata
    atan.o(i.__softfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(i.atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.____hardfp_atan$lsc) refers to fabs.o(i.fabs) for fabs
    atan_x.o(i.____hardfp_atan$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan_x.o(i.____hardfp_atan$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan_x.o(i.____hardfp_atan$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan_x.o(i.____hardfp_atan$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan_x.o(i.____hardfp_atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.____hardfp_atan$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan_x.o(i.____hardfp_atan$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan_x.o(i.____hardfp_atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.__hardfp_pow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow.o(i.__hardfp_pow) refers to _rserrno.o(.text) for __set_errno
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    pow.o(i.__hardfp_pow) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    pow.o(i.__hardfp_pow) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    pow.o(i.__hardfp_pow) refers to sqrt.o(i.sqrt) for sqrt
    pow.o(i.__hardfp_pow) refers to fabs.o(i.fabs) for fabs
    pow.o(i.__hardfp_pow) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    pow.o(i.__hardfp_pow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    pow.o(i.__hardfp_pow) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    pow.o(i.__hardfp_pow) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    pow.o(i.__hardfp_pow) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.__hardfp_pow) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    pow.o(i.__hardfp_pow) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow.o(i.__hardfp_pow) refers to pow.o(.constdata) for .constdata
    pow.o(i.__hardfp_pow) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    pow.o(i.__hardfp_pow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    pow.o(i.__hardfp_pow) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    pow.o(i.__softfp_pow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.__softfp_pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(i.pow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow_x.o(i.____hardfp_pow$lsc) refers to _rserrno.o(.text) for __set_errno
    pow_x.o(i.____hardfp_pow$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    pow_x.o(i.____hardfp_pow$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    pow_x.o(i.____hardfp_pow$lsc) refers to sqrt.o(i.sqrt) for sqrt
    pow_x.o(i.____hardfp_pow$lsc) refers to fabs.o(i.fabs) for fabs
    pow_x.o(i.____hardfp_pow$lsc) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    pow_x.o(i.____hardfp_pow$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    pow_x.o(i.____hardfp_pow$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    pow_x.o(i.____hardfp_pow$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    pow_x.o(i.____hardfp_pow$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    pow_x.o(i.____hardfp_pow$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    pow_x.o(i.____hardfp_pow$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow_x.o(i.____hardfp_pow$lsc) refers to pow_x.o(.constdata) for .constdata
    pow_x.o(i.____hardfp_pow$lsc) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    pow_x.o(i.____hardfp_pow$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    pow_x.o(i.____softfp_pow$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(i.__pow$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.__pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    tan.o(i.__hardfp_tan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan.o(i.__hardfp_tan) refers to _rserrno.o(.text) for __set_errno
    tan.o(i.__hardfp_tan) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    tan.o(i.__hardfp_tan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan.o(i.__hardfp_tan) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan.o(i.__hardfp_tan) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    tan.o(i.__softfp_tan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan.o(i.__softfp_tan) refers to _rserrno.o(.text) for __set_errno
    tan.o(i.__softfp_tan) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    tan.o(i.__softfp_tan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan.o(i.__softfp_tan) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan.o(i.__softfp_tan) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    tan.o(i.tan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan.o(i.tan) refers to _rserrno.o(.text) for __set_errno
    tan.o(i.tan) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    tan.o(i.tan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan.o(i.tan) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan.o(i.tan) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    tan_x.o(i.____hardfp_tan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan_x.o(i.____hardfp_tan$lsc) refers to _rserrno.o(.text) for __set_errno
    tan_x.o(i.____hardfp_tan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan_x.o(i.____hardfp_tan$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan_x.o(i.____hardfp_tan$lsc) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    tan_x.o(i.____softfp_tan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan_x.o(i.____softfp_tan$lsc) refers to _rserrno.o(.text) for __set_errno
    tan_x.o(i.____softfp_tan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan_x.o(i.____softfp_tan$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan_x.o(i.____softfp_tan$lsc) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    tan_x.o(i.__tan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan_x.o(i.__tan$lsc) refers to _rserrno.o(.text) for __set_errno
    tan_x.o(i.__tan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan_x.o(i.__tan$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan_x.o(i.__tan$lsc) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart.o(i.fputc) for fputc
    basic.o(x$fpl$basic) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    dsqrt_umaal.o(x$fpl$dsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_umaal.o(x$fpl$dsqrt) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    fabs.o(i.__hardfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    rred.o(i.__ieee754_rem_pio2) refers to fabs.o(i.fabs) for fabs
    rred.o(i.__ieee754_rem_pio2) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    rred.o(i.__ieee754_rem_pio2) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    rred.o(i.__ieee754_rem_pio2) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    rred.o(i.__ieee754_rem_pio2) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    rred.o(i.__ieee754_rem_pio2) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    rred.o(i.__ieee754_rem_pio2) refers to rred.o(.constdata) for .constdata
    rred.o(i.__use_accurate_range_reduction) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan_i.o(i.__kernel_tan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan_i.o(i.__kernel_tan) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    tan_i.o(i.__kernel_tan) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    tan_i.o(i.__kernel_tan) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    tan_i.o(i.__kernel_tan) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    tan_i.o(i.__kernel_tan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    tan_i.o(i.__kernel_tan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    tan_i.o(i.__kernel_tan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    tan_i.o(i.__kernel_tan) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    tan_i.o(i.__kernel_tan) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    tan_i.o(i.__kernel_tan) refers to fabs.o(i.fabs) for fabs
    tan_i.o(i.__kernel_tan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    tan_i.o(i.__kernel_tan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    tan_i.o(i.__kernel_tan) refers to tan_i.o(.constdata) for .constdata
    tan_i.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan_i_x.o(i.____kernel_tan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan_i_x.o(i.____kernel_tan$lsc) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    tan_i_x.o(i.____kernel_tan$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    tan_i_x.o(i.____kernel_tan$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    tan_i_x.o(i.____kernel_tan$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    tan_i_x.o(i.____kernel_tan$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    tan_i_x.o(i.____kernel_tan$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    tan_i_x.o(i.____kernel_tan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    tan_i_x.o(i.____kernel_tan$lsc) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    tan_i_x.o(i.____kernel_tan$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    tan_i_x.o(i.____kernel_tan$lsc) refers to fabs.o(i.fabs) for fabs
    tan_i_x.o(i.____kernel_tan$lsc) refers to tan_i_x.o(.constdata) for .constdata
    tan_i_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_char_common.o(.text) refers to __printf.o(.text) for __printf
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f407xx.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_exit.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(i._sys_exit) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.rrx_text), (6 bytes).
    Removing adc.o(i.HAL_ADC_MspDeInit), (52 bytes).
    Removing adc.o(.constdata), (4 bytes).
    Removing adc.o(.data), (4 bytes).
    Removing can.o(.rev16_text), (4 bytes).
    Removing can.o(.revsh_text), (4 bytes).
    Removing can.o(.rrx_text), (6 bytes).
    Removing can.o(i.CAN1_Msg_Pend), (32 bytes).
    Removing can.o(i.CAN1_Receive_Msg), (66 bytes).
    Removing can.o(i.CAN1_Rx_Msg), (148 bytes).
    Removing can.o(i.CAN1_Send_Msg), (58 bytes).
    Removing can.o(i.CAN1_Send_MsgTEST), (58 bytes).
    Removing can.o(i.HAL_CAN_MspDeInit), (64 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.HAL_I2C_MspDeInit), (56 bytes).
    Removing i2c.o(i.HAL_I2C_MspInit), (108 bytes).
    Removing i2c.o(i.I2C_ReadBuff), (100 bytes).
    Removing i2c.o(i.I2C_WriteBuff), (76 bytes).
    Removing i2c.o(i.I2C_WriteOneBit), (48 bytes).
    Removing i2c.o(i.MX_I2C2_Init), (72 bytes).
    Removing i2c.o(.bss), (84 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (76 bytes).
    Removing tim.o(i.HAL_TIM_Encoder_MspDeInit), (152 bytes).
    Removing tim.o(i.HAL_TIM_IC_MspDeInit), (56 bytes).
    Removing tim.o(i.HAL_TIM_PWM_MspDeInit), (68 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (168 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_adc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt), (110 bytes).
    Removing stm32f4xx_hal_adc.o(i.ADC_DMAError), (22 bytes).
    Removing stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt), (10 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig), (112 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_DeInit), (60 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetError), (4 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler), (316 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_PollForEvent), (118 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA), (340 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Start_IT), (268 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop), (60 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA), (108 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_IT), (72 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt), (90 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError), (18 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt), (6 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel), (444 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedGetValue), (58 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion), (144 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart), (220 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT), (228 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop), (86 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT), (94 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel), (84 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeGetValue), (12 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA), (268 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA), (96 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (180 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (140 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (104 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (92 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (316 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (308 bytes).
    Removing stm32f4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Byte), (32 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (44 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Word), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode), (104 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (80 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (204 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (40 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program), (124 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (108 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock), (44 bytes).
    Removing stm32f4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (80 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (84 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase), (40 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (152 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (104 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (64 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (204 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (360 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin), (10 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (30 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (14 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (100 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (2432 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift), (40 bytes).
    Removing stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam), (84 bytes).
    Removing stm32f4xx_hal_dma.o(i.DMA_SetConfig), (40 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (98 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler), (416 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Init), (212 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (288 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (80 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (70 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT), (110 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (96 bytes).
    Removing stm32f4xx_hal_dma.o(.constdata), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (56 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (88 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (84 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (52 bytes).
    Removing stm32f4xx_hal.o(i.HAL_Delay), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (108 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (148 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (168 bytes).
    Removing stm32f4xx_hal_can.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_can.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_can.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_AbortTxRequest), (70 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_ActivateNotification), (36 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_AddTxMessage), (220 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_ConfigFilter), (268 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_DeInit), (40 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_DeactivateNotification), (36 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_GetError), (4 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_GetRxFifoFillLevel), (34 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_GetRxMessage), (232 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_GetState), (36 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_GetTxMailboxesFreeLevel), (44 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_GetTxTimestamp), (40 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_IsSleepActive), (28 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_IsTxMessagePending), (30 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_MspInit), (2 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_RequestSleep), (38 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_ResetError), (34 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_Start), (88 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_Stop), (96 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_WakeUp), (76 bytes).
    Removing stm32f4xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit), (50 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (68 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (186 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (560 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (58 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetError), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetMode), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetState), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Init), (392 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (364 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (94 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive), (480 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (352 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (200 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (552 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (324 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (456 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (216 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit), (300 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (352 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (188 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read), (504 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (468 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (224 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write), (304 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (412 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (212 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (364 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (236 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (124 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (352 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (116 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (352 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (116 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (236 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (124 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAAbort), (188 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAError), (64 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt), (266 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_ITError), (344 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed), (46 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF), (210 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE), (232 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead), (236 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite), (156 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF), (130 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE), (182 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR), (280 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Master_SB), (140 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF), (160 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead), (252 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR), (70 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_AF), (132 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout), (76 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout), (104 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout), (150 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout), (102 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout), (76 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (192 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (48 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource), (220 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (216 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (332 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (332 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (106 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (106 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (428 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (182 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (102 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (172 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (144 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetChannelState), (34 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start), (228 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (460 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (268 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (160 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (146 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start), (200 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (444 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (188 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (230 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (444 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (188 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (42 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig), (20 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (140 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage), (34 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage), (36 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (144 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (34 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (208 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (224 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (180 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (58 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (70 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (68 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (192 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (392 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (232 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (170 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (100 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (98 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (192 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (392 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (232 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (170 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (28 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (26 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (94 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (108 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (128 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (142 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (254 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA), (86 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (86 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (210 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (148 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (152 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (98 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (104 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (244 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (146 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (136 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop), (112 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (52 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetState), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (192 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (42 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit), (178 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (140 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAError), (74 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt), (130 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt), (26 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt), (66 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_EndTxTransfer), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA), (144 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout), (120 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (120 bytes).
    Removing datascope_dp.o(.rev16_text), (4 bytes).
    Removing datascope_dp.o(.revsh_text), (4 bytes).
    Removing datascope_dp.o(.rrx_text), (6 bytes).
    Removing datascope_dp.o(i.DataScope_Data_Generate), (128 bytes).
    Removing datascope_dp.o(i.DataScope_Get_Channel_Data), (88 bytes).
    Removing datascope_dp.o(i.Float2Byte), (20 bytes).
    Removing datascope_dp.o(.bss), (42 bytes).
    Removing encoder.o(.rev16_text), (4 bytes).
    Removing encoder.o(.revsh_text), (4 bytes).
    Removing encoder.o(.rrx_text), (6 bytes).
    Removing exti.o(.rev16_text), (4 bytes).
    Removing exti.o(.revsh_text), (4 bytes).
    Removing exti.o(.rrx_text), (6 bytes).
    Removing exti.o(i.EXTI1_Init), (2 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(.rrx_text), (6 bytes).
    Removing key.o(i.Delay_ms), (18 bytes).
    Removing key.o(i.Long_Press), (60 bytes).
    Removing key.o(i.click), (48 bytes).
    Removing key.o(i.click_N_Double), (140 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing led.o(.rrx_text), (6 bytes).
    Removing led.o(i.Buzzer_Alarm), (32 bytes).
    Removing led.o(.data), (4 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled.o(i.OLED_Display_Off), (30 bytes).
    Removing oled.o(i.OLED_Display_On), (30 bytes).
    Removing oled.o(i.OLED_Set_Pos), (40 bytes).
    Removing oled.o(i.OLED_ShowCHinese), (84 bytes).
    Removing pstwo.o(.rev16_text), (4 bytes).
    Removing pstwo.o(.revsh_text), (4 bytes).
    Removing pstwo.o(.rrx_text), (6 bytes).
    Removing pstwo.o(i.PS2_AnologData), (12 bytes).
    Removing pstwo.o(i.PS2_Receive), (124 bytes).
    Removing pstwo.o(i.PS2_RedLight), (60 bytes).
    Removing pstwo.o(i.PS2_Vibration), (96 bytes).
    Removing pstwo.o(i.PS2_VibrationMode), (68 bytes).
    Removing timer.o(.rev16_text), (4 bytes).
    Removing timer.o(.revsh_text), (4 bytes).
    Removing timer.o(.rrx_text), (6 bytes).
    Removing timer.o(.data), (1 bytes).
    Removing timer.o(.data), (1 bytes).
    Removing timer.o(.data), (1 bytes).
    Removing timer.o(.data), (1 bytes).
    Removing timer.o(.data), (4 bytes).
    Removing timer.o(.data), (4 bytes).
    Removing timer.o(.data), (4 bytes).
    Removing timer.o(.data), (4 bytes).
    Removing timer.o(.data), (2 bytes).
    Removing timer.o(.data), (2 bytes).
    Removing timer.o(.data), (2 bytes).
    Removing timer.o(.data), (2 bytes).
    Removing timer.o(.data), (2 bytes).
    Removing timer.o(.data), (2 bytes).
    Removing timer.o(.data), (2 bytes).
    Removing timer.o(.data), (2 bytes).
    Removing timer.o(.data), (4 bytes).
    Removing timer.o(.data), (4 bytes).
    Removing timer.o(.data), (4 bytes).
    Removing timer.o(.data), (4 bytes).
    Removing usartx.o(.rev16_text), (4 bytes).
    Removing usartx.o(.revsh_text), (4 bytes).
    Removing usartx.o(.rrx_text), (6 bytes).
    Removing usartx.o(i.Vz_to_Akm_Angle), (172 bytes).
    Removing usartx.o(i.XYZ_Target_Speed_transition), (84 bytes).
    Removing usartx.o(i.usart2_send), (20 bytes).
    Removing usartx.o(.data), (1 bytes).
    Removing usartx.o(.data), (1 bytes).
    Removing usartx.o(.data), (1 bytes).
    Removing usartx.o(.data), (1 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing dma.o(.bss), (48 bytes).
    Removing dma.o(.data), (2 bytes).
    Removing balance.o(.rev16_text), (4 bytes).
    Removing balance.o(.revsh_text), (4 bytes).
    Removing balance.o(.rrx_text), (6 bytes).
    Removing balance.o(i.myabs), (8 bytes).
    Removing balance.o(i.robot_mode_check), (160 bytes).
    Removing balance.o(.data), (4 bytes).
    Removing balance.o(.data), (2 bytes).
    Removing filter.o(.rev16_text), (4 bytes).
    Removing filter.o(.revsh_text), (4 bytes).
    Removing filter.o(.rrx_text), (6 bytes).
    Removing filter.o(i.Kalman_Filter), (252 bytes).
    Removing filter.o(i.Yijielvbo), (132 bytes).
    Removing filter.o(.bss), (16 bytes).
    Removing filter.o(.data), (84 bytes).
    Removing robot_select_init.o(.rev16_text), (4 bytes).
    Removing robot_select_init.o(.revsh_text), (4 bytes).
    Removing robot_select_init.o(.rrx_text), (6 bytes).
    Removing show.o(.rev16_text), (4 bytes).
    Removing show.o(.revsh_text), (4 bytes).
    Removing show.o(.rrx_text), (6 bytes).
    Removing show.o(.data), (4 bytes).
    Removing show.o(.data), (1 bytes).
    Removing show.o(.data), (1 bytes).
    Removing system.o(.rev16_text), (4 bytes).
    Removing system.o(.revsh_text), (4 bytes).
    Removing system.o(.rrx_text), (6 bytes).
    Removing system.o(.data), (4 bytes).
    Removing system.o(.data), (4 bytes).
    Removing system.o(.data), (4 bytes).
    Removing system.o(.data), (4 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing delay.o(.rrx_text), (6 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.rrx_text), (6 bytes).
    Removing sys.o(.emb_text), (16 bytes).
    Removing mpu6050.o(.rev16_text), (4 bytes).
    Removing mpu6050.o(.revsh_text), (4 bytes).
    Removing mpu6050.o(.rrx_text), (6 bytes).
    Removing mpu6050.o(i.MPU6050_getDeviceID), (8 bytes).
    Removing mpu6050.o(i.MPU6050_newValues), (280 bytes).
    Removing mpu6050.o(i.MPU6050_setClockSource), (18 bytes).
    Removing mpu6050.o(i.MPU6050_setI2CBypassEnabled), (12 bytes).
    Removing mpu6050.o(i.MPU6050_setI2CMasterModeEnabled), (12 bytes).
    Removing mpu6050.o(i.MPU6050_setSleepEnabled), (12 bytes).
    Removing mpu6050.o(i.MPU6050_testConnection), (18 bytes).
    Removing mpu6050.o(i.Read_Temperature), (96 bytes).
    Removing mpu6050.o(.bss), (14 bytes).
    Removing mpu6050.o(.bss), (132 bytes).
    Removing mpu6050.o(.data), (4 bytes).
    Removing mpu6050.o(.data), (4 bytes).
    Removing mpu6050.o(.data), (4 bytes).
    Removing mpu6050.o(.data), (4 bytes).
    Removing mpu6050.o(.data), (2 bytes).
    Removing mpu6050.o(.data), (2 bytes).
    Removing mpu6050.o(.data), (2 bytes).
    Removing mpu6050.o(.data), (2 bytes).
    Removing mpu6050.o(.data), (4 bytes).

695 unused section(s) (total 51590 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ../Core/Src/can.c                        0x00000000   Number         0  can.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.c 0x00000000   Number         0  stm32f4xx_hal_adc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.c 0x00000000   Number         0  stm32f4xx_hal_adc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_can.c 0x00000000   Number         0  stm32f4xx_hal_can.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.c 0x00000000   Number         0  stm32f4xx_ll_adc.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/dczerorl2.s                0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/basic.s                         0x00000000   Number         0  basic.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/dsqrt.s                         0x00000000   Number         0  dsqrt_umaal.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow_x.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/rred.c                        0x00000000   Number         0  rred.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/tan.c                         0x00000000   Number         0  tan.o ABSOLUTE
    ../mathlib/tan.c                         0x00000000   Number         0  tan_x.o ABSOLUTE
    ../mathlib/tan_i.c                       0x00000000   Number         0  tan_i.o ABSOLUTE
    ../mathlib/tan_i.c                       0x00000000   Number         0  tan_i_x.o ABSOLUTE
    ..\BALANCE\balance.c                     0x00000000   Number         0  balance.o ABSOLUTE
    ..\BALANCE\filter.c                      0x00000000   Number         0  filter.o ABSOLUTE
    ..\BALANCE\robot_select_init.c           0x00000000   Number         0  robot_select_init.o ABSOLUTE
    ..\BALANCE\show.c                        0x00000000   Number         0  show.o ABSOLUTE
    ..\BALANCE\system.c                      0x00000000   Number         0  system.o ABSOLUTE
    ..\Core\Src\adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ..\Core\Src\can.c                        0x00000000   Number         0  can.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc.c 0x00000000   Number         0  stm32f4xx_hal_adc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc_ex.c 0x00000000   Number         0  stm32f4xx_hal_adc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_can.c 0x00000000   Number         0  stm32f4xx_hal_can.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\HARDWARE\DataScope_DP.C               0x00000000   Number         0  datascope_dp.o ABSOLUTE
    ..\HARDWARE\LED.C                        0x00000000   Number         0  led.o ABSOLUTE
    ..\HARDWARE\MPU6050\MPU6050.c            0x00000000   Number         0  mpu6050.o ABSOLUTE
    ..\HARDWARE\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\HARDWARE\encoder.c                    0x00000000   Number         0  encoder.o ABSOLUTE
    ..\HARDWARE\exti.c                       0x00000000   Number         0  exti.o ABSOLUTE
    ..\HARDWARE\key.c                        0x00000000   Number         0  key.o ABSOLUTE
    ..\HARDWARE\oled.c                       0x00000000   Number         0  oled.o ABSOLUTE
    ..\HARDWARE\pstwo.c                      0x00000000   Number         0  pstwo.o ABSOLUTE
    ..\HARDWARE\timer.c                      0x00000000   Number         0  timer.o ABSOLUTE
    ..\HARDWARE\usartx.c                     0x00000000   Number         0  usartx.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\\BALANCE\\balance.c                   0x00000000   Number         0  balance.o ABSOLUTE
    ..\\BALANCE\\filter.c                    0x00000000   Number         0  filter.o ABSOLUTE
    ..\\BALANCE\\robot_select_init.c         0x00000000   Number         0  robot_select_init.o ABSOLUTE
    ..\\BALANCE\\show.c                      0x00000000   Number         0  show.o ABSOLUTE
    ..\\BALANCE\\system.c                    0x00000000   Number         0  system.o ABSOLUTE
    ..\\HARDWARE\\DataScope_DP.C             0x00000000   Number         0  datascope_dp.o ABSOLUTE
    ..\\HARDWARE\\LED.C                      0x00000000   Number         0  led.o ABSOLUTE
    ..\\HARDWARE\\MPU6050\\MPU6050.c         0x00000000   Number         0  mpu6050.o ABSOLUTE
    ..\\HARDWARE\\dma.c                      0x00000000   Number         0  dma.o ABSOLUTE
    ..\\HARDWARE\\encoder.c                  0x00000000   Number         0  encoder.o ABSOLUTE
    ..\\HARDWARE\\exti.c                     0x00000000   Number         0  exti.o ABSOLUTE
    ..\\HARDWARE\\key.c                      0x00000000   Number         0  key.o ABSOLUTE
    ..\\HARDWARE\\oled.c                     0x00000000   Number         0  oled.o ABSOLUTE
    ..\\HARDWARE\\pstwo.c                    0x00000000   Number         0  pstwo.o ABSOLUTE
    ..\\HARDWARE\\timer.c                    0x00000000   Number         0  timer.o ABSOLUTE
    ..\\HARDWARE\\usartx.c                   0x00000000   Number         0  usartx.o ABSOLUTE
    ..\\SYSTEM\\delay\\delay.c               0x00000000   Number         0  delay.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    startup_stm32f407xx.s                    0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!dczerorl2                              0x080001c4   Section       90  __dczerorl2.o(!!dczerorl2)
    !!handler_zi                             0x08000220   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x0800023c   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000009  0x0800023c   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$00000017  0x08000242   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x08000246   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x08000248   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x0800024c   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x0800024e   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000250   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000250   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x08000250   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x08000250   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x08000250   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x08000250   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x08000250   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x08000250   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x08000252   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000252   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000252   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000258   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000258   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x0800025c   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x0800025c   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000264   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000266   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000266   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x0800026a   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000270   Section       64  startup_stm32f407xx.o(.text)
    $v0                                      0x08000270   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x080002b0   Section      238  lludivv7m.o(.text)
    .text                                    0x080003a0   Section        0  noretval__2printf.o(.text)
    .text                                    0x080003b8   Section        0  __printf.o(.text)
    .text                                    0x08000420   Section        0  _printf_dec.o(.text)
    .text                                    0x08000498   Section       78  rt_memclr_w.o(.text)
    .text                                    0x080004e6   Section        0  heapauxi.o(.text)
    .text                                    0x080004ec   Section        0  _rserrno.o(.text)
    .text                                    0x08000502   Section        0  _printf_intcommon.o(.text)
    .text                                    0x080005b4   Section        0  _printf_char_file.o(.text)
    .text                                    0x080005d8   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x080005e0   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x080005e1   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000610   Section        0  ferror.o(.text)
    .text                                    0x08000618   Section        8  libspace.o(.text)
    .text                                    0x08000620   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x0800066a   Section        0  exit.o(.text)
    i.ADC_Init                               0x0800067c   Section        0  stm32f4xx_hal_adc.o(i.ADC_Init)
    ADC_Init                                 0x0800067d   Thumb Code   284  stm32f4xx_hal_adc.o(i.ADC_Init)
    i.APP_Show                               0x080007a4   Section        0  show.o(i.APP_Show)
    i.BusFault_Handler                       0x080008dc   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.CAN1_RX0_IRQHandler                    0x080008e0   Section        0  stm32f4xx_it.o(i.CAN1_RX0_IRQHandler)
    i.CAN1_Send_Num                          0x080008ec   Section        0  can.o(i.CAN1_Send_Num)
    i.CAN1_Tx_Msg                            0x08000924   Section        0  can.o(i.CAN1_Tx_Msg)
    i.CAN1_Tx_Staus                          0x080009e8   Section        0  can.o(i.CAN1_Tx_Staus)
    i.CAN_SEND                               0x08000a4c   Section        0  usartx.o(i.CAN_SEND)
    i.Check_Sum                              0x08000aa4   Section        0  usartx.o(i.Check_Sum)
    i.DebugMon_Handler                       0x08000ae0   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Drive_Motor                            0x08000ae4   Section        0  balance.o(i.Drive_Motor)
    i.Error_Handler                          0x08000fba   Section        0  main.o(i.Error_Handler)
    i.Get_Adc                                0x08000fc0   Section        0  adc.o(i.Get_Adc)
    i.Get_RC                                 0x08001000   Section        0  balance.o(i.Get_RC)
    i.Get_Velocity_Form_Encoder              0x080011c4   Section        0  balance.o(i.Get_Velocity_Form_Encoder)
    i.Get_adc_Average                        0x080012d4   Section        0  adc.o(i.Get_adc_Average)
    i.Get_battery_volt                       0x08001304   Section        0  adc.o(i.Get_battery_volt)
    i.HAL_ADC_ConfigChannel                  0x0800136c   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    i.HAL_ADC_GetState                       0x080014b8   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_GetState)
    i.HAL_ADC_GetValue                       0x080014bc   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_GetValue)
    i.HAL_ADC_Init                           0x080014c2   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Init)
    i.HAL_ADC_MspInit                        0x08001518   Section        0  adc.o(i.HAL_ADC_MspInit)
    i.HAL_ADC_PollForConversion              0x08001578   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion)
    i.HAL_ADC_Start                          0x08001624   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Start)
    i.HAL_CAN_ErrorCallback                  0x08001720   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_ErrorCallback)
    i.HAL_CAN_IRQHandler                     0x08001722   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler)
    i.HAL_CAN_Init                           0x0800191e   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_Init)
    i.HAL_CAN_MspInit                        0x08001a38   Section        0  can.o(i.HAL_CAN_MspInit)
    i.HAL_CAN_RxFifo0FullCallback            0x08001ad0   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_RxFifo0FullCallback)
    i.HAL_CAN_RxFifo0MsgPendingCallback      0x08001ad2   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_RxFifo0MsgPendingCallback)
    i.HAL_CAN_RxFifo1FullCallback            0x08001ad4   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_RxFifo1FullCallback)
    i.HAL_CAN_RxFifo1MsgPendingCallback      0x08001ad6   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_RxFifo1MsgPendingCallback)
    i.HAL_CAN_SleepCallback                  0x08001ad8   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_SleepCallback)
    i.HAL_CAN_TxMailbox0AbortCallback        0x08001ada   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox0AbortCallback)
    i.HAL_CAN_TxMailbox0CompleteCallback     0x08001adc   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox0CompleteCallback)
    i.HAL_CAN_TxMailbox1AbortCallback        0x08001ade   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox1AbortCallback)
    i.HAL_CAN_TxMailbox1CompleteCallback     0x08001ae0   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox1CompleteCallback)
    i.HAL_CAN_TxMailbox2AbortCallback        0x08001ae2   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox2AbortCallback)
    i.HAL_CAN_TxMailbox2CompleteCallback     0x08001ae4   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox2CompleteCallback)
    i.HAL_CAN_WakeUpFromRxMsgCallback        0x08001ae6   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_WakeUpFromRxMsgCallback)
    i.HAL_DMA_Abort                          0x08001ae8   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x08001b7a   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_GPIO_Init                          0x08001ba0   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_WritePin                      0x08001d90   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x08001d9c   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x08001da8   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08001db8   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08001dec   Section        0  stm32f4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08001e2c   Section        0  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08001e60   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08001e7c   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08001ebc   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x08001ee0   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x08002014   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08002034   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08002054   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x080020b4   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x08002420   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_BreakCallback                0x08002448   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    i.HAL_TIMEx_CommutCallback               0x0800244a   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    i.HAL_TIMEx_ConfigBreakDeadTime          0x0800244c   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    i.HAL_TIMEx_MasterConfigSynchronization  0x080024a0   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x08002530   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x0800258c   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start_IT                  0x08002600   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_TIM_Encoder_Init                   0x08002680   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    i.HAL_TIM_Encoder_MspInit                0x08002724   Section        0  tim.o(i.HAL_TIM_Encoder_MspInit)
    i.HAL_TIM_Encoder_Start                  0x080028b0   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start)
    i.HAL_TIM_IC_CaptureCallback             0x0800293e   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IC_ConfigChannel               0x08002940   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel)
    i.HAL_TIM_IC_Init                        0x08002a5e   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init)
    i.HAL_TIM_IC_MspInit                     0x08002ab8   Section        0  tim.o(i.HAL_TIM_IC_MspInit)
    i.HAL_TIM_IRQHandler                     0x08002b30   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_MspPostInit                    0x08002c98   Section        0  tim.o(i.HAL_TIM_MspPostInit)
    i.HAL_TIM_OC_DelayElapsedCallback        0x08002d84   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_PWM_ConfigChannel              0x08002d86   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    i.HAL_TIM_PWM_Init                       0x08002e52   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    i.HAL_TIM_PWM_MspInit                    0x08002eac   Section        0  tim.o(i.HAL_TIM_PWM_MspInit)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x08002f0c   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PWM_Start                      0x08002f10   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    i.HAL_TIM_PeriodElapsedCallback          0x08002fd8   Section        0  balance.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_TriggerCallback                0x080031c4   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HAL_UARTEx_RxEventCallback             0x080031c6   Section        0  stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_ErrorCallback                 0x080031c8   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x080031cc   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x08003434   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08003498   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Receive_IT                    0x08003634   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    i.HAL_UART_RxCpltCallback                0x08003660   Section        0  usartx.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_TxCpltCallback                0x08003894   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x08003896   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.I2C_Ack                                0x08003898   Section        0  i2c.o(i.I2C_Ack)
    i.I2C_NAck                               0x080038d8   Section        0  i2c.o(i.I2C_NAck)
    i.I2C_ReadByte                           0x08003918   Section        0  i2c.o(i.I2C_ReadByte)
    i.I2C_ReadOneByte                        0x08003988   Section        0  i2c.o(i.I2C_ReadOneByte)
    i.I2C_Start                              0x080039c8   Section        0  i2c.o(i.I2C_Start)
    i.I2C_Stop                               0x08003a18   Section        0  i2c.o(i.I2C_Stop)
    i.I2C_WaiteForAck                        0x08003a58   Section        0  i2c.o(i.I2C_WaiteForAck)
    i.I2C_WriteBits                          0x08003aac   Section        0  i2c.o(i.I2C_WriteBits)
    i.I2C_WriteByte                          0x08003af8   Section        0  i2c.o(i.I2C_WriteByte)
    i.I2C_WriteOneByte                       0x08003b58   Section        0  i2c.o(i.I2C_WriteOneByte)
    i.Incremental_PI_A                       0x08003b8c   Section        0  balance.o(i.Incremental_PI_A)
    i.Incremental_PI_B                       0x08003c08   Section        0  balance.o(i.Incremental_PI_B)
    i.Incremental_PI_C                       0x08003c84   Section        0  balance.o(i.Incremental_PI_C)
    i.Incremental_PI_D                       0x08003d00   Section        0  balance.o(i.Incremental_PI_D)
    i.Key                                    0x08003d7c   Section        0  balance.o(i.Key)
    i.Led_Flash                              0x08003da0   Section        0  led.o(i.Led_Flash)
    i.Limit_Pwm                              0x08003dd0   Section        0  balance.o(i.Limit_Pwm)
    i.MPU6050_Set_LPF                        0x08003e60   Section        0  mpu6050.o(i.MPU6050_Set_LPF)
    i.MPU6050_Set_Rate                       0x08003e92   Section        0  mpu6050.o(i.MPU6050_Set_Rate)
    i.MPU6050_initialize                     0x08003ec2   Section        0  mpu6050.o(i.MPU6050_initialize)
    i.MPU6050_setFullScaleAccelRange         0x08003f46   Section        0  mpu6050.o(i.MPU6050_setFullScaleAccelRange)
    i.MPU6050_setFullScaleGyroRange          0x08003f58   Section        0  mpu6050.o(i.MPU6050_setFullScaleGyroRange)
    i.MPU6050_task                           0x08003f6a   Section        0  mpu6050.o(i.MPU6050_task)
    i.MPU_Get_Accelscope                     0x08003f78   Section        0  mpu6050.o(i.MPU_Get_Accelscope)
    i.MPU_Get_Gyroscope                      0x08003fcc   Section        0  mpu6050.o(i.MPU_Get_Gyroscope)
    i.MX_ADC1_Init                           0x08004020   Section        0  adc.o(i.MX_ADC1_Init)
    i.MX_CAN1_Init                           0x08004088   Section        0  can.o(i.MX_CAN1_Init)
    i.MX_GPIO_Init                           0x080040d0   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_TIM10_Init                          0x08004210   Section        0  tim.o(i.MX_TIM10_Init)
    i.MX_TIM11_Init                          0x0800427c   Section        0  tim.o(i.MX_TIM11_Init)
    i.MX_TIM12_Init                          0x080042e8   Section        0  tim.o(i.MX_TIM12_Init)
    i.MX_TIM1_Init                           0x08004370   Section        0  tim.o(i.MX_TIM1_Init)
    i.MX_TIM2_Init                           0x08004460   Section        0  tim.o(i.MX_TIM2_Init)
    i.MX_TIM3_Init                           0x080044d8   Section        0  tim.o(i.MX_TIM3_Init)
    i.MX_TIM4_Init                           0x08004550   Section        0  tim.o(i.MX_TIM4_Init)
    i.MX_TIM5_Init                           0x080045c8   Section        0  tim.o(i.MX_TIM5_Init)
    i.MX_TIM7_Init                           0x08004640   Section        0  tim.o(i.MX_TIM7_Init)
    i.MX_TIM8_Init                           0x08004684   Section        0  tim.o(i.MX_TIM8_Init)
    i.MX_TIM9_Init                           0x08004738   Section        0  tim.o(i.MX_TIM9_Init)
    i.MX_UART5_Init                          0x080047b0   Section        0  usart.o(i.MX_UART5_Init)
    i.MX_USART1_UART_Init                    0x080047f4   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MX_USART2_UART_Init                    0x08004838   Section        0  usart.o(i.MX_USART2_UART_Init)
    i.MX_USART3_UART_Init                    0x0800487c   Section        0  usart.o(i.MX_USART3_UART_Init)
    i.MemManage_Handler                      0x080048c0   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x080048c2   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.OLED_Clear                             0x080048c4   Section        0  oled.o(i.OLED_Clear)
    i.OLED_DrawPoint                         0x080048f0   Section        0  oled.o(i.OLED_DrawPoint)
    i.OLED_Init                              0x0800492c   Section        0  oled.o(i.OLED_Init)
    i.OLED_Refresh_Gram                      0x08004a18   Section        0  oled.o(i.OLED_Refresh_Gram)
    i.OLED_ShowChar                          0x08004a64   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowNumber                        0x08004af4   Section        0  oled.o(i.OLED_ShowNumber)
    i.OLED_ShowString                        0x08004b6c   Section        0  oled.o(i.OLED_ShowString)
    i.OLED_WR_Byte                           0x08004bac   Section        0  oled.o(i.OLED_WR_Byte)
    i.PS2_ClearData                          0x08004bf0   Section        0  pstwo.o(i.PS2_ClearData)
    i.PS2_Cmd                                0x08004c08   Section        0  pstwo.o(i.PS2_Cmd)
    i.PS2_DataKey                            0x08004c90   Section        0  pstwo.o(i.PS2_DataKey)
    i.PS2_EnterConfing                       0x08004cd0   Section        0  pstwo.o(i.PS2_EnterConfing)
    i.PS2_ExitConfing                        0x08004d2c   Section        0  pstwo.o(i.PS2_ExitConfing)
    i.PS2_Read                               0x08004d88   Section        0  pstwo.o(i.PS2_Read)
    i.PS2_ReadData                           0x08004e60   Section        0  pstwo.o(i.PS2_ReadData)
    i.PS2_SetInit                            0x08004f10   Section        0  pstwo.o(i.PS2_SetInit)
    i.PS2_ShortPoll                          0x08004f30   Section        0  pstwo.o(i.PS2_ShortPoll)
    i.PS2_TurnOnAnalogMode                   0x08004f74   Section        0  pstwo.o(i.PS2_TurnOnAnalogMode)
    i.PS2_control                            0x08004fcc   Section        0  balance.o(i.PS2_control)
    i.PendSV_Handler                         0x08005168   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.Read_Encoder                           0x0800516c   Section        0  encoder.o(i.Read_Encoder)
    i.Remote_Control                         0x080051ac   Section        0  balance.o(i.Remote_Control)
    i.Robot_Init                             0x08005374   Section        0  robot_select_init.o(i.Robot_Init)
    i.Robot_Select                           0x0800540c   Section        0  robot_select_init.o(i.Robot_Select)
    i.SVC_Handler                            0x0800556c   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.Set_Pwm                                0x08005570   Section        0  balance.o(i.Set_Pwm)
    i.Smooth_control                         0x080055e4   Section        0  balance.o(i.Smooth_control)
    i.SysTick_Handler                        0x0800571c   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08005720   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x080057b4   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TIM2_IRQHandler                        0x080057c4   Section        0  stm32f4xx_it.o(i.TIM2_IRQHandler)
    i.TIM3_IRQHandler                        0x080057d0   Section        0  stm32f4xx_it.o(i.TIM3_IRQHandler)
    i.TIM4_IRQHandler                        0x080057dc   Section        0  stm32f4xx_it.o(i.TIM4_IRQHandler)
    i.TIM5_IRQHandler                        0x080057e8   Section        0  stm32f4xx_it.o(i.TIM5_IRQHandler)
    i.TIM7_IRQHandler                        0x080057f4   Section        0  stm32f4xx_it.o(i.TIM7_IRQHandler)
    i.TIM8_CC_IRQHandler                     0x08005800   Section        0  stm32f4xx_it.o(i.TIM8_CC_IRQHandler)
    i.TIM_Base_SetConfig                     0x0800580c   Section        0  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_CCxChannelCmd                      0x080058d4   Section        0  stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    i.TIM_OC1_SetConfig                      0x080058f0   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    TIM_OC1_SetConfig                        0x080058f1   Thumb Code    88  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    i.TIM_OC2_SetConfig                      0x08005950   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    i.TIM_OC3_SetConfig                      0x080059bc   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    TIM_OC3_SetConfig                        0x080059bd   Thumb Code    96  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    i.TIM_OC4_SetConfig                      0x08005a24   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    TIM_OC4_SetConfig                        0x08005a25   Thumb Code    70  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    i.TIM_TI1_SetConfig                      0x08005a74   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig)
    i.TIM_TI2_SetConfig                      0x08005af4   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig)
    TIM_TI2_SetConfig                        0x08005af5   Thumb Code    54  stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig)
    i.Turn_Off                               0x08005b2c   Section        0  balance.o(i.Turn_Off)
    i.UART5_IRQHandler                       0x08005b7c   Section        0  stm32f4xx_it.o(i.UART5_IRQHandler)
    i.UART_DMAAbortOnError                   0x08005b88   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08005b89   Thumb Code    16  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_EndRxTransfer                     0x08005b98   Section        0  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08005b99   Thumb Code    78  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_Receive_IT                        0x08005be6   Section        0  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x08005be7   Thumb Code   192  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x08005ca8   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x08005ca9   Thumb Code   258  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_IT                  0x08005db4   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT)
    i.USART1_IRQHandler                      0x08005dec   Section        0  stm32f4xx_it.o(i.USART1_IRQHandler)
    i.USART1_SEND                            0x08005df8   Section        0  usartx.o(i.USART1_SEND)
    i.USART2_IRQHandler                      0x08005e14   Section        0  stm32f4xx_it.o(i.USART2_IRQHandler)
    i.USART3_IRQHandler                      0x08005e20   Section        0  stm32f4xx_it.o(i.USART3_IRQHandler)
    i.USART3_SEND                            0x08005e2c   Section        0  usartx.o(i.USART3_SEND)
    i.USART5_SEND                            0x08005e48   Section        0  usartx.o(i.USART5_SEND)
    i.UsageFault_Handler                     0x08005e64   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x08005e66   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__NVIC_SetPriority                     0x08005e96   Section        0  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08005e97   Thumb Code    32  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__hardfp_pow                           0x08005eb8   Section        0  pow.o(i.__hardfp_pow)
    i.__hardfp_sqrt                          0x08006b08   Section        0  sqrt.o(i.__hardfp_sqrt)
    i.__hardfp_tan                           0x08006b88   Section        0  tan.o(i.__hardfp_tan)
    i.__ieee754_rem_pio2                     0x08006c08   Section        0  rred.o(i.__ieee754_rem_pio2)
    i.__kernel_poly                          0x08007040   Section        0  poly.o(i.__kernel_poly)
    i.__kernel_tan                           0x08007138   Section        0  tan_i.o(i.__kernel_tan)
    i.__mathlib_dbl_divzero                  0x08007488   Section        0  dunder.o(i.__mathlib_dbl_divzero)
    i.__mathlib_dbl_infnan                   0x080074b8   Section        0  dunder.o(i.__mathlib_dbl_infnan)
    i.__mathlib_dbl_infnan2                  0x080074cc   Section        0  dunder.o(i.__mathlib_dbl_infnan2)
    i.__mathlib_dbl_invalid                  0x080074e0   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_overflow                 0x08007500   Section        0  dunder.o(i.__mathlib_dbl_overflow)
    i.__mathlib_dbl_underflow                0x08007520   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i._sys_exit                              0x08007540   Section        0  usart.o(i._sys_exit)
    i.click_N_Double_MPU6050                 0x08007544   Section        0  key.o(i.click_N_Double_MPU6050)
    i.data_task                              0x080075d0   Section        0  usartx.o(i.data_task)
    i.data_transition                        0x080075f4   Section        0  usartx.o(i.data_transition)
    i.delay_init                             0x08007894   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x080078b4   Section        0  delay.o(i.delay_ms)
    i.delay_us                               0x080078e8   Section        0  delay.o(i.delay_us)
    i.fabs                                   0x0800791c   Section        0  fabs.o(i.fabs)
    i.float_abs                              0x08007934   Section        0  balance.o(i.float_abs)
    i.fputc                                  0x08007944   Section        0  usart.o(i.fputc)
    i.main                                   0x08007974   Section        0  main.o(i.main)
    i.oled_pow                               0x08007a08   Section        0  oled.o(i.oled_pow)
    i.oled_show                              0x08007a18   Section        0  show.o(i.oled_show)
    i.show_task                              0x0800829c   Section        0  show.o(i.show_task)
    i.sqrt                                   0x080082e8   Section        0  sqrt.o(i.sqrt)
    i.target_limit_float                     0x08008356   Section        0  balance.o(i.target_limit_float)
    i.target_limit_int                       0x08008376   Section        0  balance.o(i.target_limit_int)
    i.usart1_send                            0x08008388   Section        0  usartx.o(i.usart1_send)
    i.usart3_send                            0x08008398   Section        0  usartx.o(i.usart3_send)
    i.usart5_send                            0x080083ac   Section        0  usartx.o(i.usart5_send)
    x$fpl$basic                              0x080083bc   Section       24  basic.o(x$fpl$basic)
    $v0                                      0x080083bc   Number         0  basic.o(x$fpl$basic)
    x$fpl$d2f                                0x080083d4   Section       98  d2f.o(x$fpl$d2f)
    $v0                                      0x080083d4   Number         0  d2f.o(x$fpl$d2f)
    x$fpl$dadd                               0x08008438   Section      336  daddsub_clz.o(x$fpl$dadd)
    $v0                                      0x08008438   Number         0  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x08008449   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dcheck1                            0x08008588   Section       16  dcheck1.o(x$fpl$dcheck1)
    $v0                                      0x08008588   Number         0  dcheck1.o(x$fpl$dcheck1)
    x$fpl$dcmpinf                            0x08008598   Section       24  dcmpi.o(x$fpl$dcmpinf)
    $v0                                      0x08008598   Number         0  dcmpi.o(x$fpl$dcmpinf)
    x$fpl$ddiv                               0x080085b0   Section      688  ddiv.o(x$fpl$ddiv)
    $v0                                      0x080085b0   Number         0  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x080085b7   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$dfix                               0x08008860   Section       94  dfix.o(x$fpl$dfix)
    $v0                                      0x08008860   Number         0  dfix.o(x$fpl$dfix)
    x$fpl$dflt                               0x080088be   Section       46  dflt_clz.o(x$fpl$dflt)
    $v0                                      0x080088be   Number         0  dflt_clz.o(x$fpl$dflt)
    x$fpl$dfltu                              0x080088ec   Section       38  dflt_clz.o(x$fpl$dfltu)
    $v0                                      0x080088ec   Number         0  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dleqf                              0x08008914   Section      120  dleqf.o(x$fpl$dleqf)
    $v0                                      0x08008914   Number         0  dleqf.o(x$fpl$dleqf)
    x$fpl$dmul                               0x0800898c   Section      340  dmul.o(x$fpl$dmul)
    $v0                                      0x0800898c   Number         0  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x08008ae0   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x08008ae0   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x08008b7c   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x08008b7c   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$drleqf                             0x08008b88   Section      108  drleqf.o(x$fpl$drleqf)
    $v0                                      0x08008b88   Number         0  drleqf.o(x$fpl$drleqf)
    x$fpl$drsb                               0x08008bf4   Section       22  daddsub_clz.o(x$fpl$drsb)
    $v0                                      0x08008bf4   Number         0  daddsub_clz.o(x$fpl$drsb)
    x$fpl$dsqrt                              0x08008c0c   Section      408  dsqrt_umaal.o(x$fpl$dsqrt)
    $v0                                      0x08008c0c   Number         0  dsqrt_umaal.o(x$fpl$dsqrt)
    x$fpl$dsub                               0x08008da4   Section      468  daddsub_clz.o(x$fpl$dsub)
    $v0                                      0x08008da4   Number         0  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x08008db5   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$f2d                                0x08008f78   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x08008f78   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x08008fce   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x08008fce   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x0800905a   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x0800905a   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$fretinf                            0x08009064   Section       10  fretinf.o(x$fpl$fretinf)
    $v0                                      0x08009064   Number         0  fretinf.o(x$fpl$fretinf)
    x$fpl$retnan                             0x0800906e   Section      100  retnan.o(x$fpl$retnan)
    $v0                                      0x0800906e   Number         0  retnan.o(x$fpl$retnan)
    x$fpl$scalbn                             0x080090d2   Section       92  scalbn.o(x$fpl$scalbn)
    $v0                                      0x080090d2   Number         0  scalbn.o(x$fpl$scalbn)
    x$fpl$trapveneer                         0x0800912e   Section       48  trapv.o(x$fpl$trapveneer)
    $v0                                      0x0800912e   Number         0  trapv.o(x$fpl$trapveneer)
    .constdata                               0x0800915e   Section       16  system_stm32f4xx.o(.constdata)
    x$fpl$usenofp                            0x0800915e   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x0800916e   Section        8  system_stm32f4xx.o(.constdata)
    .constdata                               0x08009176   Section     5284  oled.o(.constdata)
    .constdata                               0x0800a620   Section      136  pow.o(.constdata)
    bp                                       0x0800a620   Data          16  pow.o(.constdata)
    dp_h                                     0x0800a630   Data          16  pow.o(.constdata)
    dp_l                                     0x0800a640   Data          16  pow.o(.constdata)
    L                                        0x0800a650   Data          48  pow.o(.constdata)
    P                                        0x0800a680   Data          40  pow.o(.constdata)
    .constdata                               0x0800a6a8   Section        8  qnan.o(.constdata)
    .constdata                               0x0800a6b0   Section      200  rred.o(.constdata)
    pio2s                                    0x0800a6b0   Data          48  rred.o(.constdata)
    twooverpi                                0x0800a6e0   Data         152  rred.o(.constdata)
    .constdata                               0x0800a778   Section       96  tan_i.o(.constdata)
    Todd                                     0x0800a778   Data          48  tan_i.o(.constdata)
    Teven                                    0x0800a7a8   Data          48  tan_i.o(.constdata)
    .data                                    0x20000000   Section        4  adc.o(.data)
    .data                                    0x20000004   Section        4  adc.o(.data)
    .data                                    0x20000008   Section        1  usart.o(.data)
    .data                                    0x2000000c   Section        4  usart.o(.data)
    .data                                    0x20000010   Section       12  stm32f4xx_hal.o(.data)
    .data                                    0x2000001c   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x20000020   Section       20  key.o(.data)
    flag_key                                 0x20000020   Data           1  key.o(.data)
    flag_key                                 0x20000021   Data           1  key.o(.data)
    count_key                                0x20000022   Data           1  key.o(.data)
    double_key                               0x20000023   Data           1  key.o(.data)
    flag_key                                 0x20000024   Data           1  key.o(.data)
    count_key                                0x20000025   Data           1  key.o(.data)
    double_key                               0x20000026   Data           1  key.o(.data)
    count_single                             0x20000028   Data           2  key.o(.data)
    Forever_count                            0x2000002a   Data           2  key.o(.data)
    count_single                             0x2000002c   Data           2  key.o(.data)
    Forever_count                            0x2000002e   Data           2  key.o(.data)
    Long_Press_count                         0x20000030   Data           2  key.o(.data)
    Long_Press                               0x20000032   Data           2  key.o(.data)
    .data                                    0x20000034   Section        4  led.o(.data)
    temp                                     0x20000034   Data           4  led.o(.data)
    .data                                    0x20000038   Section       40  pstwo.o(.data)
    Strat                                    0x2000003c   Data           4  pstwo.o(.data)
    .data                                    0x20000060   Section        4  timer.o(.data)
    .data                                    0x20000064   Section        4  timer.o(.data)
    .data                                    0x20000068   Section        4  timer.o(.data)
    .data                                    0x2000006c   Section        4  timer.o(.data)
    .data                                    0x20000070   Section       16  usartx.o(.data)
    Flag_PID                                 0x20000070   Data           1  usartx.o(.data)
    i                                        0x20000071   Data           1  usartx.o(.data)
    j                                        0x20000072   Data           1  usartx.o(.data)
    Last_Usart_Receive                       0x20000073   Data           1  usartx.o(.data)
    Data                                     0x2000007c   Data           4  usartx.o(.data)
    .data                                    0x20000080   Section        1  usartx.o(.data)
    .data                                    0x20000081   Section        1  usartx.o(.data)
    .data                                    0x20000082   Section        1  usartx.o(.data)
    .data                                    0x20000084   Section       60  balance.o(.data)
    thrice                                   0x20000084   Data           1  balance.o(.data)
    error                                    0x20000085   Data           1  balance.o(.data)
    data_sent_count                          0x2000008c   Data           4  balance.o(.data)
    Bias                                     0x20000090   Data           4  balance.o(.data)
    Pwm                                      0x20000094   Data           4  balance.o(.data)
    Last_bias                                0x20000098   Data           4  balance.o(.data)
    Bias                                     0x2000009c   Data           4  balance.o(.data)
    Pwm                                      0x200000a0   Data           4  balance.o(.data)
    Last_bias                                0x200000a4   Data           4  balance.o(.data)
    Bias                                     0x200000a8   Data           4  balance.o(.data)
    Pwm                                      0x200000ac   Data           4  balance.o(.data)
    Last_bias                                0x200000b0   Data           4  balance.o(.data)
    Bias                                     0x200000b4   Data           4  balance.o(.data)
    Pwm                                      0x200000b8   Data           4  balance.o(.data)
    Last_bias                                0x200000bc   Data           4  balance.o(.data)
    .data                                    0x200000c0   Section       12  show.o(.data)
    flag_show                                0x200000c0   Data           1  show.o(.data)
    count                                    0x200000c4   Data           4  show.o(.data)
    .data                                    0x200000cc   Section        1  system.o(.data)
    .data                                    0x200000cd   Section        1  system.o(.data)
    .data                                    0x200000d0   Section        4  system.o(.data)
    .data                                    0x200000d4   Section        4  system.o(.data)
    .data                                    0x200000d8   Section        4  system.o(.data)
    .data                                    0x200000dc   Section        1  system.o(.data)
    .data                                    0x200000dd   Section        1  system.o(.data)
    .data                                    0x200000de   Section        1  system.o(.data)
    .data                                    0x200000df   Section        1  system.o(.data)
    .data                                    0x200000e0   Section        1  system.o(.data)
    .data                                    0x200000e4   Section        4  system.o(.data)
    .data                                    0x200000e8   Section        4  system.o(.data)
    .data                                    0x200000ec   Section        4  system.o(.data)
    .data                                    0x200000f0   Section        4  system.o(.data)
    .data                                    0x200000f4   Section        4  system.o(.data)
    .data                                    0x200000f8   Section        4  system.o(.data)
    .data                                    0x200000fc   Section        4  system.o(.data)
    .data                                    0x20000100   Section        4  system.o(.data)
    .data                                    0x20000104   Section        4  system.o(.data)
    .data                                    0x20000108   Section        4  system.o(.data)
    .data                                    0x2000010c   Section        4  system.o(.data)
    .data                                    0x20000110   Section        4  system.o(.data)
    .data                                    0x20000114   Section        4  system.o(.data)
    .data                                    0x20000118   Section        1  system.o(.data)
    .data                                    0x20000119   Section        1  system.o(.data)
    .data                                    0x2000011a   Section        1  system.o(.data)
    .data                                    0x2000011b   Section        1  system.o(.data)
    .data                                    0x2000011c   Section        1  system.o(.data)
    .data                                    0x2000011d   Section        1  system.o(.data)
    .data                                    0x20000120   Section        4  system.o(.data)
    .data                                    0x20000124   Section        4  system.o(.data)
    .data                                    0x20000128   Section        4  system.o(.data)
    .data                                    0x2000012c   Section        4  system.o(.data)
    .data                                    0x20000130   Section        4  system.o(.data)
    .data                                    0x20000134   Section        4  delay.o(.data)
    fac_us                                   0x20000134   Data           1  delay.o(.data)
    fac_ms                                   0x20000136   Data           2  delay.o(.data)
    .data                                    0x20000138   Section       12  mpu6050.o(.data)
    .data                                    0x20000144   Section        6  mpu6050.o(.data)
    .data                                    0x2000014a   Section        6  mpu6050.o(.data)
    .bss                                     0x20000150   Section       72  adc.o(.bss)
    .bss                                     0x20000198   Section       40  can.o(.bss)
    .bss                                     0x200001c0   Section      792  tim.o(.bss)
    .bss                                     0x200004d8   Section      272  usart.o(.bss)
    .bss                                     0x200005e8   Section     1024  oled.o(.bss)
    .bss                                     0x200009e8   Section        9  pstwo.o(.bss)
    .bss                                     0x200009f4   Section      132  usartx.o(.bss)
    Receive                                  0x200009f4   Data          50  usartx.o(.bss)
    .bss                                     0x20000a78   Section       16  balance.o(.bss)
    .bss                                     0x20000a88   Section       24  robot_select_init.o(.bss)
    .bss                                     0x20000aa0   Section       12  system.o(.bss)
    .bss                                     0x20000aac   Section       20  system.o(.bss)
    .bss                                     0x20000ac0   Section       20  system.o(.bss)
    .bss                                     0x20000ad4   Section       20  system.o(.bss)
    .bss                                     0x20000ae8   Section       20  system.o(.bss)
    .bss                                     0x20000afc   Section       96  libspace.o(.bss)
    HEAP                                     0x20000b60   Section      512  startup_stm32f407xx.o(HEAP)
    Heap_Mem                                 0x20000b60   Data         512  startup_stm32f407xx.o(HEAP)
    STACK                                    0x20000d60   Section     1024  startup_stm32f407xx.o(STACK)
    Stack_Mem                                0x20000d60   Data        1024  startup_stm32f407xx.o(STACK)
    __initial_sp                             0x20001160   Data           0  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __decompress                             0x080001c5   Thumb Code    90  __dczerorl2.o(!!dczerorl2)
    __decompress1                            0x080001c5   Thumb Code     0  __dczerorl2.o(!!dczerorl2)
    __scatterload_zeroinit                   0x08000221   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_d                                0x0800023d   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_percent                          0x0800023d   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_percent_end                      0x08000243   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x08000247   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_alloca_1                   0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x0800024f   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000251   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x08000251   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000251   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x08000251   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x08000251   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x08000251   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x08000251   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x08000251   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x08000253   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000253   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000253   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000259   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000259   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x0800025d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x0800025d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000265   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000267   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000267   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x0800026b   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000271   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART4_IRQHandler                         0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART6_IRQHandler                        0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x0800028b   Thumb Code     0  startup_stm32f407xx.o(.text)
    __user_initial_stackheap                 0x0800028d   Thumb Code     0  startup_stm32f407xx.o(.text)
    __aeabi_uldivmod                         0x080002b1   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x080002b1   Thumb Code   238  lludivv7m.o(.text)
    __2printf                                0x080003a1   Thumb Code    20  noretval__2printf.o(.text)
    __printf                                 0x080003b9   Thumb Code   104  __printf.o(.text)
    _printf_int_dec                          0x08000421   Thumb Code   104  _printf_dec.o(.text)
    __aeabi_memclr4                          0x08000499   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x08000499   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x08000499   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x0800049d   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x080004e7   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080004e9   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080004eb   Thumb Code     2  heapauxi.o(.text)
    __read_errno                             0x080004ed   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x080004f7   Thumb Code    12  _rserrno.o(.text)
    _printf_int_common                       0x08000503   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_char_file                        0x080005b5   Thumb Code    32  _printf_char_file.o(.text)
    __aeabi_errno_addr                       0x080005d9   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x080005d9   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x080005d9   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    _printf_char_common                      0x080005eb   Thumb Code    32  _printf_char_common.o(.text)
    ferror                                   0x08000611   Thumb Code     8  ferror.o(.text)
    __user_libspace                          0x08000619   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000619   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000619   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08000621   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x0800066b   Thumb Code    18  exit.o(.text)
    APP_Show                                 0x080007a5   Thumb Code   214  show.o(i.APP_Show)
    BusFault_Handler                         0x080008dd   Thumb Code     2  stm32f4xx_it.o(i.BusFault_Handler)
    CAN1_RX0_IRQHandler                      0x080008e1   Thumb Code     6  stm32f4xx_it.o(i.CAN1_RX0_IRQHandler)
    CAN1_Send_Num                            0x080008ed   Thumb Code    54  can.o(i.CAN1_Send_Num)
    CAN1_Tx_Msg                              0x08000925   Thumb Code   188  can.o(i.CAN1_Tx_Msg)
    CAN1_Tx_Staus                            0x080009e9   Thumb Code    94  can.o(i.CAN1_Tx_Staus)
    CAN_SEND                                 0x08000a4d   Thumb Code    84  usartx.o(i.CAN_SEND)
    Check_Sum                                0x08000aa5   Thumb Code    54  usartx.o(i.Check_Sum)
    DebugMon_Handler                         0x08000ae1   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Drive_Motor                              0x08000ae5   Thumb Code  1238  balance.o(i.Drive_Motor)
    Error_Handler                            0x08000fbb   Thumb Code     4  main.o(i.Error_Handler)
    Get_Adc                                  0x08000fc1   Thumb Code    58  adc.o(i.Get_Adc)
    Get_RC                                   0x08001001   Thumb Code   398  balance.o(i.Get_RC)
    Get_Velocity_Form_Encoder                0x080011c5   Thumb Code   236  balance.o(i.Get_Velocity_Form_Encoder)
    Get_adc_Average                          0x080012d5   Thumb Code    46  adc.o(i.Get_adc_Average)
    Get_battery_volt                         0x08001305   Thumb Code    70  adc.o(i.Get_battery_volt)
    HAL_ADC_ConfigChannel                    0x0800136d   Thumb Code   316  stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    HAL_ADC_GetState                         0x080014b9   Thumb Code     4  stm32f4xx_hal_adc.o(i.HAL_ADC_GetState)
    HAL_ADC_GetValue                         0x080014bd   Thumb Code     6  stm32f4xx_hal_adc.o(i.HAL_ADC_GetValue)
    HAL_ADC_Init                             0x080014c3   Thumb Code    84  stm32f4xx_hal_adc.o(i.HAL_ADC_Init)
    HAL_ADC_MspInit                          0x08001519   Thumb Code    82  adc.o(i.HAL_ADC_MspInit)
    HAL_ADC_PollForConversion                0x08001579   Thumb Code   170  stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion)
    HAL_ADC_Start                            0x08001625   Thumb Code   228  stm32f4xx_hal_adc.o(i.HAL_ADC_Start)
    HAL_CAN_ErrorCallback                    0x08001721   Thumb Code     2  stm32f4xx_hal_can.o(i.HAL_CAN_ErrorCallback)
    HAL_CAN_IRQHandler                       0x08001723   Thumb Code   508  stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler)
    HAL_CAN_Init                             0x0800191f   Thumb Code   282  stm32f4xx_hal_can.o(i.HAL_CAN_Init)
    HAL_CAN_MspInit                          0x08001a39   Thumb Code   136  can.o(i.HAL_CAN_MspInit)
    HAL_CAN_RxFifo0FullCallback              0x08001ad1   Thumb Code     2  stm32f4xx_hal_can.o(i.HAL_CAN_RxFifo0FullCallback)
    HAL_CAN_RxFifo0MsgPendingCallback        0x08001ad3   Thumb Code     2  stm32f4xx_hal_can.o(i.HAL_CAN_RxFifo0MsgPendingCallback)
    HAL_CAN_RxFifo1FullCallback              0x08001ad5   Thumb Code     2  stm32f4xx_hal_can.o(i.HAL_CAN_RxFifo1FullCallback)
    HAL_CAN_RxFifo1MsgPendingCallback        0x08001ad7   Thumb Code     2  stm32f4xx_hal_can.o(i.HAL_CAN_RxFifo1MsgPendingCallback)
    HAL_CAN_SleepCallback                    0x08001ad9   Thumb Code     2  stm32f4xx_hal_can.o(i.HAL_CAN_SleepCallback)
    HAL_CAN_TxMailbox0AbortCallback          0x08001adb   Thumb Code     2  stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox0AbortCallback)
    HAL_CAN_TxMailbox0CompleteCallback       0x08001add   Thumb Code     2  stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox0CompleteCallback)
    HAL_CAN_TxMailbox1AbortCallback          0x08001adf   Thumb Code     2  stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox1AbortCallback)
    HAL_CAN_TxMailbox1CompleteCallback       0x08001ae1   Thumb Code     2  stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox1CompleteCallback)
    HAL_CAN_TxMailbox2AbortCallback          0x08001ae3   Thumb Code     2  stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox2AbortCallback)
    HAL_CAN_TxMailbox2CompleteCallback       0x08001ae5   Thumb Code     2  stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox2CompleteCallback)
    HAL_CAN_WakeUpFromRxMsgCallback          0x08001ae7   Thumb Code     2  stm32f4xx_hal_can.o(i.HAL_CAN_WakeUpFromRxMsgCallback)
    HAL_DMA_Abort                            0x08001ae9   Thumb Code   146  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08001b7b   Thumb Code    36  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_GPIO_Init                            0x08001ba1   Thumb Code   450  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_WritePin                        0x08001d91   Thumb Code    10  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08001d9d   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x08001da9   Thumb Code    12  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08001db9   Thumb Code    48  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08001ded   Thumb Code    54  stm32f4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08001e2d   Thumb Code    48  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08001e61   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08001e7d   Thumb Code    60  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08001ebd   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08001ee1   Thumb Code   288  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x08002015   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08002035   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08002055   Thumb Code    88  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x080020b5   Thumb Code   856  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x08002421   Thumb Code    40  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_BreakCallback                  0x08002449   Thumb Code     2  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x0800244b   Thumb Code     2  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    HAL_TIMEx_ConfigBreakDeadTime            0x0800244d   Thumb Code    84  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    HAL_TIMEx_MasterConfigSynchronization    0x080024a1   Thumb Code   116  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x08002531   Thumb Code    90  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x0800258d   Thumb Code    98  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start_IT                    0x08002601   Thumb Code   100  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_TIM_Encoder_Init                     0x08002681   Thumb Code   164  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    HAL_TIM_Encoder_MspInit                  0x08002725   Thumb Code   370  tim.o(i.HAL_TIM_Encoder_MspInit)
    HAL_TIM_Encoder_Start                    0x080028b1   Thumb Code   142  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start)
    HAL_TIM_IC_CaptureCallback               0x0800293f   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IC_ConfigChannel                 0x08002941   Thumb Code   286  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel)
    HAL_TIM_IC_Init                          0x08002a5f   Thumb Code    90  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init)
    HAL_TIM_IC_MspInit                       0x08002ab9   Thumb Code   108  tim.o(i.HAL_TIM_IC_MspInit)
    HAL_TIM_IRQHandler                       0x08002b31   Thumb Code   358  stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_MspPostInit                      0x08002c99   Thumb Code   202  tim.o(i.HAL_TIM_MspPostInit)
    HAL_TIM_OC_DelayElapsedCallback          0x08002d85   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_ConfigChannel                0x08002d87   Thumb Code   204  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x08002e53   Thumb Code    90  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x08002ead   Thumb Code    78  tim.o(i.HAL_TIM_PWM_MspInit)
    HAL_TIM_PWM_PulseFinishedCallback        0x08002f0d   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PWM_Start                        0x08002f11   Thumb Code   172  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    HAL_TIM_PeriodElapsedCallback            0x08002fd9   Thumb Code   422  balance.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_TriggerCallback                  0x080031c5   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HAL_UARTEx_RxEventCallback               0x080031c7   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_ErrorCallback                   0x080031c9   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x080031cd   Thumb Code   610  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08003435   Thumb Code    98  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08003499   Thumb Code   380  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Receive_IT                      0x08003635   Thumb Code    42  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x08003661   Thumb Code   474  usartx.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_TxCpltCallback                  0x08003895   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08003897   Thumb Code     2  stm32f4xx_it.o(i.HardFault_Handler)
    I2C_Ack                                  0x08003899   Thumb Code    54  i2c.o(i.I2C_Ack)
    I2C_NAck                                 0x080038d9   Thumb Code    56  i2c.o(i.I2C_NAck)
    I2C_ReadByte                             0x08003919   Thumb Code   102  i2c.o(i.I2C_ReadByte)
    I2C_ReadOneByte                          0x08003989   Thumb Code    62  i2c.o(i.I2C_ReadOneByte)
    I2C_Start                                0x080039c9   Thumb Code    70  i2c.o(i.I2C_Start)
    I2C_Stop                                 0x08003a19   Thumb Code    56  i2c.o(i.I2C_Stop)
    I2C_WaiteForAck                          0x08003a59   Thumb Code    76  i2c.o(i.I2C_WaiteForAck)
    I2C_WriteBits                            0x08003aad   Thumb Code    74  i2c.o(i.I2C_WriteBits)
    I2C_WriteByte                            0x08003af9   Thumb Code    86  i2c.o(i.I2C_WriteByte)
    I2C_WriteOneByte                         0x08003b59   Thumb Code    50  i2c.o(i.I2C_WriteOneByte)
    Incremental_PI_A                         0x08003b8d   Thumb Code    96  balance.o(i.Incremental_PI_A)
    Incremental_PI_B                         0x08003c09   Thumb Code    96  balance.o(i.Incremental_PI_B)
    Incremental_PI_C                         0x08003c85   Thumb Code    96  balance.o(i.Incremental_PI_C)
    Incremental_PI_D                         0x08003d01   Thumb Code    96  balance.o(i.Incremental_PI_D)
    Key                                      0x08003d7d   Thumb Code    26  balance.o(i.Key)
    Led_Flash                                0x08003da1   Thumb Code    40  led.o(i.Led_Flash)
    Limit_Pwm                                0x08003dd1   Thumb Code   126  balance.o(i.Limit_Pwm)
    MPU6050_Set_LPF                          0x08003e61   Thumb Code    50  mpu6050.o(i.MPU6050_Set_LPF)
    MPU6050_Set_Rate                         0x08003e93   Thumb Code    48  mpu6050.o(i.MPU6050_Set_Rate)
    MPU6050_initialize                       0x08003ec3   Thumb Code   132  mpu6050.o(i.MPU6050_initialize)
    MPU6050_setFullScaleAccelRange           0x08003f47   Thumb Code    18  mpu6050.o(i.MPU6050_setFullScaleAccelRange)
    MPU6050_setFullScaleGyroRange            0x08003f59   Thumb Code    18  mpu6050.o(i.MPU6050_setFullScaleGyroRange)
    MPU6050_task                             0x08003f6b   Thumb Code    14  mpu6050.o(i.MPU6050_task)
    MPU_Get_Accelscope                       0x08003f79   Thumb Code    78  mpu6050.o(i.MPU_Get_Accelscope)
    MPU_Get_Gyroscope                        0x08003fcd   Thumb Code    78  mpu6050.o(i.MPU_Get_Gyroscope)
    MX_ADC1_Init                             0x08004021   Thumb Code    92  adc.o(i.MX_ADC1_Init)
    MX_CAN1_Init                             0x08004089   Thumb Code    62  can.o(i.MX_CAN1_Init)
    MX_GPIO_Init                             0x080040d1   Thumb Code   300  gpio.o(i.MX_GPIO_Init)
    MX_TIM10_Init                            0x08004211   Thumb Code   100  tim.o(i.MX_TIM10_Init)
    MX_TIM11_Init                            0x0800427d   Thumb Code   100  tim.o(i.MX_TIM11_Init)
    MX_TIM12_Init                            0x080042e9   Thumb Code   128  tim.o(i.MX_TIM12_Init)
    MX_TIM1_Init                             0x08004371   Thumb Code   230  tim.o(i.MX_TIM1_Init)
    MX_TIM2_Init                             0x08004461   Thumb Code   114  tim.o(i.MX_TIM2_Init)
    MX_TIM3_Init                             0x080044d9   Thumb Code   112  tim.o(i.MX_TIM3_Init)
    MX_TIM4_Init                             0x08004551   Thumb Code   112  tim.o(i.MX_TIM4_Init)
    MX_TIM5_Init                             0x080045c9   Thumb Code   112  tim.o(i.MX_TIM5_Init)
    MX_TIM7_Init                             0x08004641   Thumb Code    60  tim.o(i.MX_TIM7_Init)
    MX_TIM8_Init                             0x08004685   Thumb Code   172  tim.o(i.MX_TIM8_Init)
    MX_TIM9_Init                             0x08004739   Thumb Code   112  tim.o(i.MX_TIM9_Init)
    MX_UART5_Init                            0x080047b1   Thumb Code    54  usart.o(i.MX_UART5_Init)
    MX_USART1_UART_Init                      0x080047f5   Thumb Code    54  usart.o(i.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x08004839   Thumb Code    54  usart.o(i.MX_USART2_UART_Init)
    MX_USART3_UART_Init                      0x0800487d   Thumb Code    54  usart.o(i.MX_USART3_UART_Init)
    MemManage_Handler                        0x080048c1   Thumb Code     2  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x080048c3   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    OLED_Clear                               0x080048c5   Thumb Code    40  oled.o(i.OLED_Clear)
    OLED_DrawPoint                           0x080048f1   Thumb Code    54  oled.o(i.OLED_DrawPoint)
    OLED_Init                                0x0800492d   Thumb Code   232  oled.o(i.OLED_Init)
    OLED_Refresh_Gram                        0x08004a19   Thumb Code    70  oled.o(i.OLED_Refresh_Gram)
    OLED_ShowChar                            0x08004a65   Thumb Code   134  oled.o(i.OLED_ShowChar)
    OLED_ShowNumber                          0x08004af5   Thumb Code   120  oled.o(i.OLED_ShowNumber)
    OLED_ShowString                          0x08004b6d   Thumb Code    62  oled.o(i.OLED_ShowString)
    OLED_WR_Byte                             0x08004bad   Thumb Code    64  oled.o(i.OLED_WR_Byte)
    PS2_ClearData                            0x08004bf1   Thumb Code    18  pstwo.o(i.PS2_ClearData)
    PS2_Cmd                                  0x08004c09   Thumb Code   126  pstwo.o(i.PS2_Cmd)
    PS2_DataKey                              0x08004c91   Thumb Code    56  pstwo.o(i.PS2_DataKey)
    PS2_EnterConfing                         0x08004cd1   Thumb Code    88  pstwo.o(i.PS2_EnterConfing)
    PS2_ExitConfing                          0x08004d2d   Thumb Code    88  pstwo.o(i.PS2_ExitConfing)
    PS2_Read                                 0x08004d89   Thumb Code   158  pstwo.o(i.PS2_Read)
    PS2_ReadData                             0x08004e61   Thumb Code   164  pstwo.o(i.PS2_ReadData)
    PS2_SetInit                              0x08004f11   Thumb Code    30  pstwo.o(i.PS2_SetInit)
    PS2_ShortPoll                            0x08004f31   Thumb Code    64  pstwo.o(i.PS2_ShortPoll)
    PS2_TurnOnAnalogMode                     0x08004f75   Thumb Code    82  pstwo.o(i.PS2_TurnOnAnalogMode)
    PS2_control                              0x08004fcd   Thumb Code   344  balance.o(i.PS2_control)
    PendSV_Handler                           0x08005169   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    Read_Encoder                             0x0800516d   Thumb Code    52  encoder.o(i.Read_Encoder)
    Remote_Control                           0x080051ad   Thumb Code   398  balance.o(i.Remote_Control)
    Robot_Init                               0x08005375   Thumb Code   124  robot_select_init.o(i.Robot_Init)
    Robot_Select                             0x0800540d   Thumb Code   250  robot_select_init.o(i.Robot_Select)
    SVC_Handler                              0x0800556d   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    Set_Pwm                                  0x08005571   Thumb Code    94  balance.o(i.Set_Pwm)
    Smooth_control                           0x080055e5   Thumb Code   300  balance.o(i.Smooth_control)
    SysTick_Handler                          0x0800571d   Thumb Code     4  stm32f4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08005721   Thumb Code   138  main.o(i.SystemClock_Config)
    SystemInit                               0x080057b5   Thumb Code    12  system_stm32f4xx.o(i.SystemInit)
    TIM2_IRQHandler                          0x080057c5   Thumb Code     6  stm32f4xx_it.o(i.TIM2_IRQHandler)
    TIM3_IRQHandler                          0x080057d1   Thumb Code     6  stm32f4xx_it.o(i.TIM3_IRQHandler)
    TIM4_IRQHandler                          0x080057dd   Thumb Code     6  stm32f4xx_it.o(i.TIM4_IRQHandler)
    TIM5_IRQHandler                          0x080057e9   Thumb Code     6  stm32f4xx_it.o(i.TIM5_IRQHandler)
    TIM7_IRQHandler                          0x080057f5   Thumb Code     6  stm32f4xx_it.o(i.TIM7_IRQHandler)
    TIM8_CC_IRQHandler                       0x08005801   Thumb Code     6  stm32f4xx_it.o(i.TIM8_CC_IRQHandler)
    TIM_Base_SetConfig                       0x0800580d   Thumb Code   156  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_CCxChannelCmd                        0x080058d5   Thumb Code    26  stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    TIM_OC2_SetConfig                        0x08005951   Thumb Code    98  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    TIM_TI1_SetConfig                        0x08005a75   Thumb Code   100  stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig)
    Turn_Off                                 0x08005b2d   Thumb Code    54  balance.o(i.Turn_Off)
    UART5_IRQHandler                         0x08005b7d   Thumb Code     6  stm32f4xx_it.o(i.UART5_IRQHandler)
    UART_Start_Receive_IT                    0x08005db5   Thumb Code    54  stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT)
    USART1_IRQHandler                        0x08005ded   Thumb Code     6  stm32f4xx_it.o(i.USART1_IRQHandler)
    USART1_SEND                              0x08005df9   Thumb Code    22  usartx.o(i.USART1_SEND)
    USART2_IRQHandler                        0x08005e15   Thumb Code     6  stm32f4xx_it.o(i.USART2_IRQHandler)
    USART3_IRQHandler                        0x08005e21   Thumb Code     6  stm32f4xx_it.o(i.USART3_IRQHandler)
    USART3_SEND                              0x08005e2d   Thumb Code    22  usartx.o(i.USART3_SEND)
    USART5_SEND                              0x08005e49   Thumb Code    22  usartx.o(i.USART5_SEND)
    UsageFault_Handler                       0x08005e65   Thumb Code     2  stm32f4xx_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x08005e67   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __hardfp_pow                             0x08005eb9   Thumb Code  3072  pow.o(i.__hardfp_pow)
    __hardfp_sqrt                            0x08006b09   Thumb Code   122  sqrt.o(i.__hardfp_sqrt)
    __hardfp_tan                             0x08006b89   Thumb Code   108  tan.o(i.__hardfp_tan)
    __ieee754_rem_pio2                       0x08006c09   Thumb Code   938  rred.o(i.__ieee754_rem_pio2)
    __kernel_poly                            0x08007041   Thumb Code   248  poly.o(i.__kernel_poly)
    __kernel_tan                             0x08007139   Thumb Code   764  tan_i.o(i.__kernel_tan)
    __mathlib_dbl_divzero                    0x08007489   Thumb Code    28  dunder.o(i.__mathlib_dbl_divzero)
    __mathlib_dbl_infnan                     0x080074b9   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan)
    __mathlib_dbl_infnan2                    0x080074cd   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan2)
    __mathlib_dbl_invalid                    0x080074e1   Thumb Code    24  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_overflow                   0x08007501   Thumb Code    24  dunder.o(i.__mathlib_dbl_overflow)
    __mathlib_dbl_underflow                  0x08007521   Thumb Code    24  dunder.o(i.__mathlib_dbl_underflow)
    _sys_exit                                0x08007541   Thumb Code     2  usart.o(i._sys_exit)
    click_N_Double_MPU6050                   0x08007545   Thumb Code   132  key.o(i.click_N_Double_MPU6050)
    data_task                                0x080075d1   Thumb Code    30  usartx.o(i.data_task)
    data_transition                          0x080075f5   Thumb Code   592  usartx.o(i.data_transition)
    delay_init                               0x08007895   Thumb Code    26  delay.o(i.delay_init)
    delay_ms                                 0x080078b5   Thumb Code    46  delay.o(i.delay_ms)
    delay_us                                 0x080078e9   Thumb Code    46  delay.o(i.delay_us)
    fabs                                     0x0800791d   Thumb Code    24  fabs.o(i.fabs)
    float_abs                                0x08007935   Thumb Code    16  balance.o(i.float_abs)
    fputc                                    0x08007945   Thumb Code    36  usart.o(i.fputc)
    main                                     0x08007975   Thumb Code   140  main.o(i.main)
    oled_pow                                 0x08007a09   Thumb Code    16  oled.o(i.oled_pow)
    oled_show                                0x08007a19   Thumb Code  2044  show.o(i.oled_show)
    show_task                                0x0800829d   Thumb Code    64  show.o(i.show_task)
    sqrt                                     0x080082e9   Thumb Code   110  sqrt.o(i.sqrt)
    target_limit_float                       0x08008357   Thumb Code    32  balance.o(i.target_limit_float)
    target_limit_int                         0x08008377   Thumb Code    16  balance.o(i.target_limit_int)
    usart1_send                              0x08008389   Thumb Code    12  usartx.o(i.usart1_send)
    usart3_send                              0x08008399   Thumb Code    14  usartx.o(i.usart3_send)
    usart5_send                              0x080083ad   Thumb Code    12  usartx.o(i.usart5_send)
    __aeabi_dneg                             0x080083bd   Thumb Code     0  basic.o(x$fpl$basic)
    _dneg                                    0x080083bd   Thumb Code     6  basic.o(x$fpl$basic)
    __aeabi_fneg                             0x080083c3   Thumb Code     0  basic.o(x$fpl$basic)
    _fneg                                    0x080083c3   Thumb Code     6  basic.o(x$fpl$basic)
    _dabs                                    0x080083c9   Thumb Code     6  basic.o(x$fpl$basic)
    _fabs                                    0x080083cf   Thumb Code     6  basic.o(x$fpl$basic)
    __aeabi_d2f                              0x080083d5   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x080083d5   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_dadd                             0x08008439   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x08008439   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dcheck_NaN1                        0x08008589   Thumb Code    10  dcheck1.o(x$fpl$dcheck1)
    __fpl_dcmp_Inf                           0x08008599   Thumb Code    24  dcmpi.o(x$fpl$dcmpinf)
    __aeabi_ddiv                             0x080085b1   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x080085b1   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_d2iz                             0x08008861   Thumb Code     0  dfix.o(x$fpl$dfix)
    _dfix                                    0x08008861   Thumb Code    94  dfix.o(x$fpl$dfix)
    __aeabi_i2d                              0x080088bf   Thumb Code     0  dflt_clz.o(x$fpl$dflt)
    _dflt                                    0x080088bf   Thumb Code    46  dflt_clz.o(x$fpl$dflt)
    __aeabi_ui2d                             0x080088ed   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x080088ed   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __aeabi_cdcmple                          0x08008915   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    _dcmple                                  0x08008915   Thumb Code   120  dleqf.o(x$fpl$dleqf)
    __fpl_dcmple_InfNaN                      0x08008977   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    __aeabi_dmul                             0x0800898d   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x0800898d   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x08008ae1   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x08008b7d   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_cdrcmple                         0x08008b89   Thumb Code     0  drleqf.o(x$fpl$drleqf)
    _drcmple                                 0x08008b89   Thumb Code   108  drleqf.o(x$fpl$drleqf)
    __aeabi_drsub                            0x08008bf5   Thumb Code     0  daddsub_clz.o(x$fpl$drsb)
    _drsb                                    0x08008bf5   Thumb Code    22  daddsub_clz.o(x$fpl$drsb)
    _dsqrt                                   0x08008c0d   Thumb Code   404  dsqrt_umaal.o(x$fpl$dsqrt)
    __aeabi_dsub                             0x08008da5   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x08008da5   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    __aeabi_f2d                              0x08008f79   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x08008f79   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x08008fcf   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x0800905b   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x08009063   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x08009063   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fpl_fretinf                            0x08009065   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __fpl_return_NaN                         0x0800906f   Thumb Code   100  retnan.o(x$fpl$retnan)
    __ARM_scalbn                             0x080090d3   Thumb Code    92  scalbn.o(x$fpl$scalbn)
    __fpl_cmpreturn                          0x0800912f   Thumb Code    48  trapv.o(x$fpl$trapveneer)
    AHBPrescTable                            0x0800915e   Data          16  system_stm32f4xx.o(.constdata)
    __I$use$fp                               0x0800915e   Number         0  usenofp.o(x$fpl$usenofp)
    APBPrescTable                            0x0800916e   Data           8  system_stm32f4xx.o(.constdata)
    oled_asc2_1206                           0x08009176   Data        1140  oled.o(.constdata)
    oled_asc2_1608                           0x080095ea   Data        1520  oled.o(.constdata)
    Hzk16                                    0x08009bda   Data        2624  oled.o(.constdata)
    __mathlib_zero                           0x0800a6a8   Data           8  qnan.o(.constdata)
    Region$$Table$$Base                      0x0800a7d8   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800a7f8   Number         0  anon$$obj.o(Region$$Table)
    Voltage                                  0x20000000   Data           4  adc.o(.data)
    Voltage_All                              0x20000004   Data           4  adc.o(.data)
    Flag_Show                                0x20000008   Data           1  usart.o(.data)
    __stdout                                 0x2000000c   Data           4  usart.o(.data)
    uwTickFreq                               0x20000010   Data           1  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x20000014   Data           4  stm32f4xx_hal.o(.data)
    uwTick                                   0x20000018   Data           4  stm32f4xx_hal.o(.data)
    SystemCoreClock                          0x2000001c   Data           4  system_stm32f4xx.o(.data)
    Comd                                     0x20000038   Data           2  pstwo.o(.data)
    Handkey                                  0x2000003a   Data           2  pstwo.o(.data)
    MASK                                     0x20000040   Data          32  pstwo.o(.data)
    Remoter_Ch1                              0x20000060   Data           4  timer.o(.data)
    Remoter_Ch2                              0x20000064   Data           4  timer.o(.data)
    Remoter_Ch3                              0x20000068   Data           4  timer.o(.data)
    Remoter_Ch4                              0x2000006c   Data           4  timer.o(.data)
    Usart2_Receive_buf                       0x20000074   Data           1  usartx.o(.data)
    data_sent_flag                           0x20000078   Data           4  usartx.o(.data)
    Usart1_Receive_buf                       0x20000080   Data           1  usartx.o(.data)
    Usart3_Receive_buf                       0x20000081   Data           1  usartx.o(.data)
    Uart5_Receive_buf                        0x20000082   Data           1  usartx.o(.data)
    robot_mode_check_flag                    0x20000088   Data           4  balance.o(.data)
    Voltage_Show                             0x200000c8   Data           4  show.o(.data)
    Flag_Stop                                0x200000cc   Data           1  system.o(.data)
    Car_Mode                                 0x200000cd   Data           1  system.o(.data)
    RC_Velocity                              0x200000d0   Data           4  system.o(.data)
    Velocity_KP                              0x200000d4   Data           4  system.o(.data)
    Velocity_KI                              0x200000d8   Data           4  system.o(.data)
    PS2_ON_Flag                              0x200000dc   Data           1  system.o(.data)
    APP_ON_Flag                              0x200000dd   Data           1  system.o(.data)
    Remote_ON_Flag                           0x200000de   Data           1  system.o(.data)
    CAN_ON_Flag                              0x200000df   Data           1  system.o(.data)
    Flag_Direction                           0x200000e0   Data           1  system.o(.data)
    Check                                    0x200000e4   Data           4  system.o(.data)
    CheckPhrase1                             0x200000e8   Data           4  system.o(.data)
    CheckPhrase2                             0x200000ec   Data           4  system.o(.data)
    Divisor_Mode                             0x200000f0   Data           4  system.o(.data)
    Servo                                    0x200000f4   Data           4  system.o(.data)
    Move_X                                   0x200000f8   Data           4  system.o(.data)
    Move_Y                                   0x200000fc   Data           4  system.o(.data)
    Move_Z                                   0x20000100   Data           4  system.o(.data)
    Encoder_precision                        0x20000104   Data           4  system.o(.data)
    Wheel_perimeter                          0x20000108   Data           4  system.o(.data)
    Wheel_spacing                            0x2000010c   Data           4  system.o(.data)
    Axle_spacing                             0x20000110   Data           4  system.o(.data)
    Omni_turn_radiaus                        0x20000114   Data           4  system.o(.data)
    Usart1_ON_Flag                           0x20000118   Data           1  system.o(.data)
    Usart5_ON_Flag                           0x20000119   Data           1  system.o(.data)
    Flag_Left                                0x2000011a   Data           1  system.o(.data)
    Flag_Right                               0x2000011b   Data           1  system.o(.data)
    Turn_Flag                                0x2000011c   Data           1  system.o(.data)
    PID_Send                                 0x2000011d   Data           1  system.o(.data)
    PS2_LX                                   0x20000120   Data           4  system.o(.data)
    PS2_LY                                   0x20000124   Data           4  system.o(.data)
    PS2_RX                                   0x20000128   Data           4  system.o(.data)
    PS2_RY                                   0x2000012c   Data           4  system.o(.data)
    PS2_KEY                                  0x20000130   Data           4  system.o(.data)
    gyro                                     0x20000138   Data           6  mpu6050.o(.data)
    accel                                    0x2000013e   Data           6  mpu6050.o(.data)
    Deviation_gyro                           0x20000144   Data           6  mpu6050.o(.data)
    Original_gyro                            0x2000014a   Data           6  mpu6050.o(.data)
    hadc1                                    0x20000150   Data          72  adc.o(.bss)
    hcan1                                    0x20000198   Data          40  can.o(.bss)
    htim1                                    0x200001c0   Data          72  tim.o(.bss)
    htim2                                    0x20000208   Data          72  tim.o(.bss)
    htim3                                    0x20000250   Data          72  tim.o(.bss)
    htim4                                    0x20000298   Data          72  tim.o(.bss)
    htim5                                    0x200002e0   Data          72  tim.o(.bss)
    htim7                                    0x20000328   Data          72  tim.o(.bss)
    htim8                                    0x20000370   Data          72  tim.o(.bss)
    htim9                                    0x200003b8   Data          72  tim.o(.bss)
    htim10                                   0x20000400   Data          72  tim.o(.bss)
    htim11                                   0x20000448   Data          72  tim.o(.bss)
    htim12                                   0x20000490   Data          72  tim.o(.bss)
    huart5                                   0x200004d8   Data          68  usart.o(.bss)
    huart1                                   0x2000051c   Data          68  usart.o(.bss)
    huart2                                   0x20000560   Data          68  usart.o(.bss)
    huart3                                   0x200005a4   Data          68  usart.o(.bss)
    OLED_GRAM                                0x200005e8   Data        1024  oled.o(.bss)
    Data                                     0x200009e8   Data           9  pstwo.o(.bss)
    Send_Data                                0x20000a26   Data          48  usartx.o(.bss)
    Receive_Data                             0x20000a58   Data          32  usartx.o(.bss)
    OriginalEncoder                          0x20000a78   Data          16  balance.o(.bss)
    Robot_Parament                           0x20000a88   Data          24  robot_select_init.o(.bss)
    smooth_control                           0x20000aa0   Data          12  system.o(.bss)
    MOTOR_A                                  0x20000aac   Data          20  system.o(.bss)
    MOTOR_B                                  0x20000ac0   Data          20  system.o(.bss)
    MOTOR_C                                  0x20000ad4   Data          20  system.o(.bss)
    MOTOR_D                                  0x20000ae8   Data          20  system.o(.bss)
    __libspace_start                         0x20000afc   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000b5c   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000a948, Max: 0x00080000, ABSOLUTE, COMPRESSED[0x0000a838])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000a7f8, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000008   Code   RO         5730  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         6060    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000005a   Code   RO         6058    !!dczerorl2         c_w.l(__dczerorl2.o)
    0x0800021e   0x0800021e   0x00000002   PAD
    0x08000220   0x08000220   0x0000001c   Code   RO         6062    !!handler_zi        c_w.l(__scatter_zi.o)
    0x0800023c   0x0800023c   0x00000000   Code   RO         5725    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x0800023c   0x0800023c   0x00000006   Code   RO         5724    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000242   0x08000242   0x00000004   Code   RO         5821    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x08000246   0x08000246   0x00000002   Code   RO         5927    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000248   0x08000248   0x00000004   Code   RO         5938    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         5941    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         5944    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         5946    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         5948    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         5951    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         5953    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         5955    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         5957    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         5959    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         5961    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         5963    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         5965    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         5967    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         5969    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         5971    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         5975    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         5977    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         5979    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         5981    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000002   Code   RO         5982    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x0800024e   0x0800024e   0x00000002   Code   RO         6010    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000250   0x08000250   0x00000000   Code   RO         6039    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000250   0x08000250   0x00000000   Code   RO         6041    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000250   0x08000250   0x00000000   Code   RO         6043    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x08000250   0x08000250   0x00000000   Code   RO         6046    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x08000250   0x08000250   0x00000000   Code   RO         6049    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000250   0x08000250   0x00000000   Code   RO         6051    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x08000250   0x08000250   0x00000000   Code   RO         6054    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x08000250   0x08000250   0x00000002   Code   RO         6055    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         5810    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000252   0x08000252   0x00000000   Code   RO         5878    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000252   0x08000252   0x00000006   Code   RO         5890    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x08000258   0x08000258   0x00000000   Code   RO         5880    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x08000258   0x08000258   0x00000004   Code   RO         5881    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x0800025c   0x0800025c   0x00000000   Code   RO         5883    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x0800025c   0x0800025c   0x00000008   Code   RO         5884    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000264   0x08000264   0x00000002   Code   RO         5930    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000266   0x08000266   0x00000000   Code   RO         5986    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000266   0x08000266   0x00000004   Code   RO         5987    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x0800026a   0x0800026a   0x00000006   Code   RO         5988    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000270   0x08000270   0x00000040   Code   RO            4    .text               startup_stm32f407xx.o
    0x080002b0   0x080002b0   0x000000ee   Code   RO         5694    .text               c_w.l(lludivv7m.o)
    0x0800039e   0x0800039e   0x00000002   PAD
    0x080003a0   0x080003a0   0x00000018   Code   RO         5698    .text               c_w.l(noretval__2printf.o)
    0x080003b8   0x080003b8   0x00000068   Code   RO         5700    .text               c_w.l(__printf.o)
    0x08000420   0x08000420   0x00000078   Code   RO         5702    .text               c_w.l(_printf_dec.o)
    0x08000498   0x08000498   0x0000004e   Code   RO         5726    .text               c_w.l(rt_memclr_w.o)
    0x080004e6   0x080004e6   0x00000006   Code   RO         5728    .text               c_w.l(heapauxi.o)
    0x080004ec   0x080004ec   0x00000016   Code   RO         5815    .text               c_w.l(_rserrno.o)
    0x08000502   0x08000502   0x000000b2   Code   RO         5817    .text               c_w.l(_printf_intcommon.o)
    0x080005b4   0x080005b4   0x00000024   Code   RO         5819    .text               c_w.l(_printf_char_file.o)
    0x080005d8   0x080005d8   0x00000008   Code   RO         5897    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x080005e0   0x080005e0   0x00000030   Code   RO         5899    .text               c_w.l(_printf_char_common.o)
    0x08000610   0x08000610   0x00000008   Code   RO         5901    .text               c_w.l(ferror.o)
    0x08000618   0x08000618   0x00000008   Code   RO         5909    .text               c_w.l(libspace.o)
    0x08000620   0x08000620   0x0000004a   Code   RO         5912    .text               c_w.l(sys_stackheap_outer.o)
    0x0800066a   0x0800066a   0x00000012   Code   RO         5916    .text               c_w.l(exit.o)
    0x0800067c   0x0800067c   0x00000128   Code   RO          948    i.ADC_Init          stm32f4xx_hal_adc.o
    0x080007a4   0x080007a4   0x00000138   Code   RO         5394    i.APP_Show          show.o
    0x080008dc   0x080008dc   0x00000002   Code   RO          782    i.BusFault_Handler  stm32f4xx_it.o
    0x080008de   0x080008de   0x00000002   PAD
    0x080008e0   0x080008e0   0x0000000c   Code   RO          783    i.CAN1_RX0_IRQHandler  stm32f4xx_it.o
    0x080008ec   0x080008ec   0x00000036   Code   RO          355    i.CAN1_Send_Num     can.o
    0x08000922   0x08000922   0x00000002   PAD
    0x08000924   0x08000924   0x000000c4   Code   RO          356    i.CAN1_Tx_Msg       can.o
    0x080009e8   0x080009e8   0x00000064   Code   RO          357    i.CAN1_Tx_Staus     can.o
    0x08000a4c   0x08000a4c   0x00000058   Code   RO         4848    i.CAN_SEND          usartx.o
    0x08000aa4   0x08000aa4   0x0000003c   Code   RO         4849    i.Check_Sum         usartx.o
    0x08000ae0   0x08000ae0   0x00000002   Code   RO          784    i.DebugMon_Handler  stm32f4xx_it.o
    0x08000ae2   0x08000ae2   0x00000002   PAD
    0x08000ae4   0x08000ae4   0x000004d6   Code   RO         4997    i.Drive_Motor       balance.o
    0x08000fba   0x08000fba   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x08000fbe   0x08000fbe   0x00000002   PAD
    0x08000fc0   0x08000fc0   0x00000040   Code   RO          286    i.Get_Adc           adc.o
    0x08001000   0x08001000   0x000001c4   Code   RO         4998    i.Get_RC            balance.o
    0x080011c4   0x080011c4   0x00000110   Code   RO         4999    i.Get_Velocity_Form_Encoder  balance.o
    0x080012d4   0x080012d4   0x0000002e   Code   RO          287    i.Get_adc_Average   adc.o
    0x08001302   0x08001302   0x00000002   PAD
    0x08001304   0x08001304   0x00000068   Code   RO          288    i.Get_battery_volt  adc.o
    0x0800136c   0x0800136c   0x0000014c   Code   RO          950    i.HAL_ADC_ConfigChannel  stm32f4xx_hal_adc.o
    0x080014b8   0x080014b8   0x00000004   Code   RO          956    i.HAL_ADC_GetState  stm32f4xx_hal_adc.o
    0x080014bc   0x080014bc   0x00000006   Code   RO          957    i.HAL_ADC_GetValue  stm32f4xx_hal_adc.o
    0x080014c2   0x080014c2   0x00000054   Code   RO          959    i.HAL_ADC_Init      stm32f4xx_hal_adc.o
    0x08001516   0x08001516   0x00000002   PAD
    0x08001518   0x08001518   0x00000060   Code   RO          290    i.HAL_ADC_MspInit   adc.o
    0x08001578   0x08001578   0x000000aa   Code   RO          963    i.HAL_ADC_PollForConversion  stm32f4xx_hal_adc.o
    0x08001622   0x08001622   0x00000002   PAD
    0x08001624   0x08001624   0x000000fc   Code   RO          965    i.HAL_ADC_Start     stm32f4xx_hal_adc.o
    0x08001720   0x08001720   0x00000002   Code   RO         2361    i.HAL_CAN_ErrorCallback  stm32f4xx_hal_can.o
    0x08001722   0x08001722   0x000001fc   Code   RO         2368    i.HAL_CAN_IRQHandler  stm32f4xx_hal_can.o
    0x0800191e   0x0800191e   0x0000011a   Code   RO         2369    i.HAL_CAN_Init      stm32f4xx_hal_can.o
    0x08001a38   0x08001a38   0x00000098   Code   RO          359    i.HAL_CAN_MspInit   can.o
    0x08001ad0   0x08001ad0   0x00000002   Code   RO         2376    i.HAL_CAN_RxFifo0FullCallback  stm32f4xx_hal_can.o
    0x08001ad2   0x08001ad2   0x00000002   Code   RO         2377    i.HAL_CAN_RxFifo0MsgPendingCallback  stm32f4xx_hal_can.o
    0x08001ad4   0x08001ad4   0x00000002   Code   RO         2378    i.HAL_CAN_RxFifo1FullCallback  stm32f4xx_hal_can.o
    0x08001ad6   0x08001ad6   0x00000002   Code   RO         2379    i.HAL_CAN_RxFifo1MsgPendingCallback  stm32f4xx_hal_can.o
    0x08001ad8   0x08001ad8   0x00000002   Code   RO         2380    i.HAL_CAN_SleepCallback  stm32f4xx_hal_can.o
    0x08001ada   0x08001ada   0x00000002   Code   RO         2383    i.HAL_CAN_TxMailbox0AbortCallback  stm32f4xx_hal_can.o
    0x08001adc   0x08001adc   0x00000002   Code   RO         2384    i.HAL_CAN_TxMailbox0CompleteCallback  stm32f4xx_hal_can.o
    0x08001ade   0x08001ade   0x00000002   Code   RO         2385    i.HAL_CAN_TxMailbox1AbortCallback  stm32f4xx_hal_can.o
    0x08001ae0   0x08001ae0   0x00000002   Code   RO         2386    i.HAL_CAN_TxMailbox1CompleteCallback  stm32f4xx_hal_can.o
    0x08001ae2   0x08001ae2   0x00000002   Code   RO         2387    i.HAL_CAN_TxMailbox2AbortCallback  stm32f4xx_hal_can.o
    0x08001ae4   0x08001ae4   0x00000002   Code   RO         2388    i.HAL_CAN_TxMailbox2CompleteCallback  stm32f4xx_hal_can.o
    0x08001ae6   0x08001ae6   0x00000002   Code   RO         2390    i.HAL_CAN_WakeUpFromRxMsgCallback  stm32f4xx_hal_can.o
    0x08001ae8   0x08001ae8   0x00000092   Code   RO         1685    i.HAL_DMA_Abort     stm32f4xx_hal_dma.o
    0x08001b7a   0x08001b7a   0x00000024   Code   RO         1686    i.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x08001b9e   0x08001b9e   0x00000002   PAD
    0x08001ba0   0x08001ba0   0x000001f0   Code   RO         1578    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x08001d90   0x08001d90   0x0000000a   Code   RO         1582    i.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x08001d9a   0x08001d9a   0x00000002   PAD
    0x08001d9c   0x08001d9c   0x0000000c   Code   RO         2110    i.HAL_GetTick       stm32f4xx_hal.o
    0x08001da8   0x08001da8   0x00000010   Code   RO         2116    i.HAL_IncTick       stm32f4xx_hal.o
    0x08001db8   0x08001db8   0x00000034   Code   RO         2117    i.HAL_Init          stm32f4xx_hal.o
    0x08001dec   0x08001dec   0x00000040   Code   RO         2118    i.HAL_InitTick      stm32f4xx_hal.o
    0x08001e2c   0x08001e2c   0x00000034   Code   RO          921    i.HAL_MspInit       stm32f4xx_hal_msp.o
    0x08001e60   0x08001e60   0x0000001a   Code   RO         1967    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x08001e7a   0x08001e7a   0x00000002   PAD
    0x08001e7c   0x08001e7c   0x00000040   Code   RO         1973    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08001ebc   0x08001ebc   0x00000024   Code   RO         1974    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08001ee0   0x08001ee0   0x00000134   Code   RO         1224    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x08002014   0x08002014   0x00000020   Code   RO         1231    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x08002034   0x08002034   0x00000020   Code   RO         1232    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x08002054   0x08002054   0x00000060   Code   RO         1233    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x080020b4   0x080020b4   0x0000036c   Code   RO         1236    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x08002420   0x08002420   0x00000028   Code   RO         1978    i.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x08002448   0x08002448   0x00000002   Code   RO         3745    i.HAL_TIMEx_BreakCallback  stm32f4xx_hal_tim_ex.o
    0x0800244a   0x0800244a   0x00000002   Code   RO         3746    i.HAL_TIMEx_CommutCallback  stm32f4xx_hal_tim_ex.o
    0x0800244c   0x0800244c   0x00000054   Code   RO         3748    i.HAL_TIMEx_ConfigBreakDeadTime  stm32f4xx_hal_tim_ex.o
    0x080024a0   0x080024a0   0x00000090   Code   RO         3764    i.HAL_TIMEx_MasterConfigSynchronization  stm32f4xx_hal_tim_ex.o
    0x08002530   0x08002530   0x0000005a   Code   RO         3041    i.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x0800258a   0x0800258a   0x00000002   PAD
    0x0800258c   0x0800258c   0x00000074   Code   RO          561    i.HAL_TIM_Base_MspInit  tim.o
    0x08002600   0x08002600   0x00000080   Code   RO         3046    i.HAL_TIM_Base_Start_IT  stm32f4xx_hal_tim.o
    0x08002680   0x08002680   0x000000a4   Code   RO         3062    i.HAL_TIM_Encoder_Init  stm32f4xx_hal_tim.o
    0x08002724   0x08002724   0x0000018c   Code   RO          563    i.HAL_TIM_Encoder_MspInit  tim.o
    0x080028b0   0x080028b0   0x0000008e   Code   RO         3065    i.HAL_TIM_Encoder_Start  stm32f4xx_hal_tim.o
    0x0800293e   0x0800293e   0x00000002   Code   RO         3075    i.HAL_TIM_IC_CaptureCallback  stm32f4xx_hal_tim.o
    0x08002940   0x08002940   0x0000011e   Code   RO         3077    i.HAL_TIM_IC_ConfigChannel  stm32f4xx_hal_tim.o
    0x08002a5e   0x08002a5e   0x0000005a   Code   RO         3080    i.HAL_TIM_IC_Init   stm32f4xx_hal_tim.o
    0x08002ab8   0x08002ab8   0x00000078   Code   RO          565    i.HAL_TIM_IC_MspInit  tim.o
    0x08002b30   0x08002b30   0x00000166   Code   RO         3089    i.HAL_TIM_IRQHandler  stm32f4xx_hal_tim.o
    0x08002c96   0x08002c96   0x00000002   PAD
    0x08002c98   0x08002c98   0x000000ec   Code   RO          566    i.HAL_TIM_MspPostInit  tim.o
    0x08002d84   0x08002d84   0x00000002   Code   RO         3092    i.HAL_TIM_OC_DelayElapsedCallback  stm32f4xx_hal_tim.o
    0x08002d86   0x08002d86   0x000000cc   Code   RO         3113    i.HAL_TIM_PWM_ConfigChannel  stm32f4xx_hal_tim.o
    0x08002e52   0x08002e52   0x0000005a   Code   RO         3116    i.HAL_TIM_PWM_Init  stm32f4xx_hal_tim.o
    0x08002eac   0x08002eac   0x00000060   Code   RO          568    i.HAL_TIM_PWM_MspInit  tim.o
    0x08002f0c   0x08002f0c   0x00000002   Code   RO         3119    i.HAL_TIM_PWM_PulseFinishedCallback  stm32f4xx_hal_tim.o
    0x08002f0e   0x08002f0e   0x00000002   PAD
    0x08002f10   0x08002f10   0x000000c8   Code   RO         3121    i.HAL_TIM_PWM_Start  stm32f4xx_hal_tim.o
    0x08002fd8   0x08002fd8   0x000001ec   Code   RO         5000    i.HAL_TIM_PeriodElapsedCallback  balance.o
    0x080031c4   0x080031c4   0x00000002   Code   RO         3132    i.HAL_TIM_TriggerCallback  stm32f4xx_hal_tim.o
    0x080031c6   0x080031c6   0x00000002   Code   RO         4023    i.HAL_UARTEx_RxEventCallback  stm32f4xx_hal_uart.o
    0x080031c8   0x080031c8   0x00000002   Code   RO         4037    i.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x080031ca   0x080031ca   0x00000002   PAD
    0x080031cc   0x080031cc   0x00000268   Code   RO         4040    i.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x08003434   0x08003434   0x00000062   Code   RO         4041    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x08003496   0x08003496   0x00000002   PAD
    0x08003498   0x08003498   0x0000019c   Code   RO          705    i.HAL_UART_MspInit  usart.o
    0x08003634   0x08003634   0x0000002a   Code   RO         4046    i.HAL_UART_Receive_IT  stm32f4xx_hal_uart.o
    0x0800365e   0x0800365e   0x00000002   PAD
    0x08003660   0x08003660   0x00000234   Code   RO         4850    i.HAL_UART_RxCpltCallback  usartx.o
    0x08003894   0x08003894   0x00000002   Code   RO         4052    i.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x08003896   0x08003896   0x00000002   Code   RO          785    i.HardFault_Handler  stm32f4xx_it.o
    0x08003898   0x08003898   0x00000040   Code   RO          442    i.I2C_Ack           i2c.o
    0x080038d8   0x080038d8   0x00000040   Code   RO          443    i.I2C_NAck          i2c.o
    0x08003918   0x08003918   0x00000070   Code   RO          445    i.I2C_ReadByte      i2c.o
    0x08003988   0x08003988   0x0000003e   Code   RO          446    i.I2C_ReadOneByte   i2c.o
    0x080039c6   0x080039c6   0x00000002   PAD
    0x080039c8   0x080039c8   0x00000050   Code   RO          447    i.I2C_Start         i2c.o
    0x08003a18   0x08003a18   0x00000040   Code   RO          448    i.I2C_Stop          i2c.o
    0x08003a58   0x08003a58   0x00000054   Code   RO          449    i.I2C_WaiteForAck   i2c.o
    0x08003aac   0x08003aac   0x0000004a   Code   RO          450    i.I2C_WriteBits     i2c.o
    0x08003af6   0x08003af6   0x00000002   PAD
    0x08003af8   0x08003af8   0x00000060   Code   RO          452    i.I2C_WriteByte     i2c.o
    0x08003b58   0x08003b58   0x00000032   Code   RO          454    i.I2C_WriteOneByte  i2c.o
    0x08003b8a   0x08003b8a   0x00000002   PAD
    0x08003b8c   0x08003b8c   0x0000007c   Code   RO         5001    i.Incremental_PI_A  balance.o
    0x08003c08   0x08003c08   0x0000007c   Code   RO         5002    i.Incremental_PI_B  balance.o
    0x08003c84   0x08003c84   0x0000007c   Code   RO         5003    i.Incremental_PI_C  balance.o
    0x08003d00   0x08003d00   0x0000007c   Code   RO         5004    i.Incremental_PI_D  balance.o
    0x08003d7c   0x08003d7c   0x00000024   Code   RO         5005    i.Key               balance.o
    0x08003da0   0x08003da0   0x00000030   Code   RO         4550    i.Led_Flash         led.o
    0x08003dd0   0x08003dd0   0x00000090   Code   RO         5006    i.Limit_Pwm         balance.o
    0x08003e60   0x08003e60   0x00000032   Code   RO         5563    i.MPU6050_Set_LPF   mpu6050.o
    0x08003e92   0x08003e92   0x00000030   Code   RO         5564    i.MPU6050_Set_Rate  mpu6050.o
    0x08003ec2   0x08003ec2   0x00000084   Code   RO         5566    i.MPU6050_initialize  mpu6050.o
    0x08003f46   0x08003f46   0x00000012   Code   RO         5569    i.MPU6050_setFullScaleAccelRange  mpu6050.o
    0x08003f58   0x08003f58   0x00000012   Code   RO         5570    i.MPU6050_setFullScaleGyroRange  mpu6050.o
    0x08003f6a   0x08003f6a   0x0000000e   Code   RO         5574    i.MPU6050_task      mpu6050.o
    0x08003f78   0x08003f78   0x00000054   Code   RO         5576    i.MPU_Get_Accelscope  mpu6050.o
    0x08003fcc   0x08003fcc   0x00000054   Code   RO         5577    i.MPU_Get_Gyroscope  mpu6050.o
    0x08004020   0x08004020   0x00000068   Code   RO          291    i.MX_ADC1_Init      adc.o
    0x08004088   0x08004088   0x00000048   Code   RO          360    i.MX_CAN1_Init      can.o
    0x080040d0   0x080040d0   0x00000140   Code   RO          262    i.MX_GPIO_Init      gpio.o
    0x08004210   0x08004210   0x0000006c   Code   RO          569    i.MX_TIM10_Init     tim.o
    0x0800427c   0x0800427c   0x0000006c   Code   RO          570    i.MX_TIM11_Init     tim.o
    0x080042e8   0x080042e8   0x00000088   Code   RO          571    i.MX_TIM12_Init     tim.o
    0x08004370   0x08004370   0x000000f0   Code   RO          572    i.MX_TIM1_Init      tim.o
    0x08004460   0x08004460   0x00000078   Code   RO          573    i.MX_TIM2_Init      tim.o
    0x080044d8   0x080044d8   0x00000078   Code   RO          574    i.MX_TIM3_Init      tim.o
    0x08004550   0x08004550   0x00000078   Code   RO          575    i.MX_TIM4_Init      tim.o
    0x080045c8   0x080045c8   0x00000078   Code   RO          576    i.MX_TIM5_Init      tim.o
    0x08004640   0x08004640   0x00000044   Code   RO          577    i.MX_TIM7_Init      tim.o
    0x08004684   0x08004684   0x000000b4   Code   RO          578    i.MX_TIM8_Init      tim.o
    0x08004738   0x08004738   0x00000078   Code   RO          579    i.MX_TIM9_Init      tim.o
    0x080047b0   0x080047b0   0x00000044   Code   RO          706    i.MX_UART5_Init     usart.o
    0x080047f4   0x080047f4   0x00000044   Code   RO          707    i.MX_USART1_UART_Init  usart.o
    0x08004838   0x08004838   0x00000044   Code   RO          708    i.MX_USART2_UART_Init  usart.o
    0x0800487c   0x0800487c   0x00000044   Code   RO          709    i.MX_USART3_UART_Init  usart.o
    0x080048c0   0x080048c0   0x00000002   Code   RO          786    i.MemManage_Handler  stm32f4xx_it.o
    0x080048c2   0x080048c2   0x00000002   Code   RO          787    i.NMI_Handler       stm32f4xx_it.o
    0x080048c4   0x080048c4   0x0000002c   Code   RO         4586    i.OLED_Clear        oled.o
    0x080048f0   0x080048f0   0x0000003c   Code   RO         4589    i.OLED_DrawPoint    oled.o
    0x0800492c   0x0800492c   0x000000ec   Code   RO         4590    i.OLED_Init         oled.o
    0x08004a18   0x08004a18   0x0000004c   Code   RO         4591    i.OLED_Refresh_Gram  oled.o
    0x08004a64   0x08004a64   0x00000090   Code   RO         4594    i.OLED_ShowChar     oled.o
    0x08004af4   0x08004af4   0x00000078   Code   RO         4595    i.OLED_ShowNumber   oled.o
    0x08004b6c   0x08004b6c   0x0000003e   Code   RO         4596    i.OLED_ShowString   oled.o
    0x08004baa   0x08004baa   0x00000002   PAD
    0x08004bac   0x08004bac   0x00000044   Code   RO         4597    i.OLED_WR_Byte      oled.o
    0x08004bf0   0x08004bf0   0x00000018   Code   RO         4688    i.PS2_ClearData     pstwo.o
    0x08004c08   0x08004c08   0x00000088   Code   RO         4689    i.PS2_Cmd           pstwo.o
    0x08004c90   0x08004c90   0x00000040   Code   RO         4690    i.PS2_DataKey       pstwo.o
    0x08004cd0   0x08004cd0   0x0000005c   Code   RO         4691    i.PS2_EnterConfing  pstwo.o
    0x08004d2c   0x08004d2c   0x0000005c   Code   RO         4692    i.PS2_ExitConfing   pstwo.o
    0x08004d88   0x08004d88   0x000000d8   Code   RO         4693    i.PS2_Read          pstwo.o
    0x08004e60   0x08004e60   0x000000b0   Code   RO         4694    i.PS2_ReadData      pstwo.o
    0x08004f10   0x08004f10   0x0000001e   Code   RO         4697    i.PS2_SetInit       pstwo.o
    0x08004f2e   0x08004f2e   0x00000002   PAD
    0x08004f30   0x08004f30   0x00000044   Code   RO         4698    i.PS2_ShortPoll     pstwo.o
    0x08004f74   0x08004f74   0x00000058   Code   RO         4699    i.PS2_TurnOnAnalogMode  pstwo.o
    0x08004fcc   0x08004fcc   0x0000019c   Code   RO         5007    i.PS2_control       balance.o
    0x08005168   0x08005168   0x00000002   Code   RO          788    i.PendSV_Handler    stm32f4xx_it.o
    0x0800516a   0x0800516a   0x00000002   PAD
    0x0800516c   0x0800516c   0x00000040   Code   RO         4449    i.Read_Encoder      encoder.o
    0x080051ac   0x080051ac   0x000001c8   Code   RO         5008    i.Remote_Control    balance.o
    0x08005374   0x08005374   0x00000098   Code   RO         5178    i.Robot_Init        robot_select_init.o
    0x0800540c   0x0800540c   0x00000160   Code   RO         5179    i.Robot_Select      robot_select_init.o
    0x0800556c   0x0800556c   0x00000002   Code   RO          789    i.SVC_Handler       stm32f4xx_it.o
    0x0800556e   0x0800556e   0x00000002   PAD
    0x08005570   0x08005570   0x00000074   Code   RO         5009    i.Set_Pwm           balance.o
    0x080055e4   0x080055e4   0x00000138   Code   RO         5010    i.Smooth_control    balance.o
    0x0800571c   0x0800571c   0x00000004   Code   RO          790    i.SysTick_Handler   stm32f4xx_it.o
    0x08005720   0x08005720   0x00000094   Code   RO           14    i.SystemClock_Config  main.o
    0x080057b4   0x080057b4   0x00000010   Code   RO         4370    i.SystemInit        system_stm32f4xx.o
    0x080057c4   0x080057c4   0x0000000c   Code   RO          791    i.TIM2_IRQHandler   stm32f4xx_it.o
    0x080057d0   0x080057d0   0x0000000c   Code   RO          792    i.TIM3_IRQHandler   stm32f4xx_it.o
    0x080057dc   0x080057dc   0x0000000c   Code   RO          793    i.TIM4_IRQHandler   stm32f4xx_it.o
    0x080057e8   0x080057e8   0x0000000c   Code   RO          794    i.TIM5_IRQHandler   stm32f4xx_it.o
    0x080057f4   0x080057f4   0x0000000c   Code   RO          795    i.TIM7_IRQHandler   stm32f4xx_it.o
    0x08005800   0x08005800   0x0000000c   Code   RO          796    i.TIM8_CC_IRQHandler  stm32f4xx_it.o
    0x0800580c   0x0800580c   0x000000c8   Code   RO         3134    i.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x080058d4   0x080058d4   0x0000001a   Code   RO         3135    i.TIM_CCxChannelCmd  stm32f4xx_hal_tim.o
    0x080058ee   0x080058ee   0x00000002   PAD
    0x080058f0   0x080058f0   0x00000060   Code   RO         3147    i.TIM_OC1_SetConfig  stm32f4xx_hal_tim.o
    0x08005950   0x08005950   0x0000006c   Code   RO         3148    i.TIM_OC2_SetConfig  stm32f4xx_hal_tim.o
    0x080059bc   0x080059bc   0x00000068   Code   RO         3149    i.TIM_OC3_SetConfig  stm32f4xx_hal_tim.o
    0x08005a24   0x08005a24   0x00000050   Code   RO         3150    i.TIM_OC4_SetConfig  stm32f4xx_hal_tim.o
    0x08005a74   0x08005a74   0x00000080   Code   RO         3153    i.TIM_TI1_SetConfig  stm32f4xx_hal_tim.o
    0x08005af4   0x08005af4   0x00000036   Code   RO         3155    i.TIM_TI2_SetConfig  stm32f4xx_hal_tim.o
    0x08005b2a   0x08005b2a   0x00000002   PAD
    0x08005b2c   0x08005b2c   0x00000050   Code   RO         5011    i.Turn_Off          balance.o
    0x08005b7c   0x08005b7c   0x0000000c   Code   RO          797    i.UART5_IRQHandler  stm32f4xx_it.o
    0x08005b88   0x08005b88   0x00000010   Code   RO         4054    i.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x08005b98   0x08005b98   0x0000004e   Code   RO         4064    i.UART_EndRxTransfer  stm32f4xx_hal_uart.o
    0x08005be6   0x08005be6   0x000000c0   Code   RO         4066    i.UART_Receive_IT   stm32f4xx_hal_uart.o
    0x08005ca6   0x08005ca6   0x00000002   PAD
    0x08005ca8   0x08005ca8   0x0000010c   Code   RO         4067    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x08005db4   0x08005db4   0x00000036   Code   RO         4069    i.UART_Start_Receive_IT  stm32f4xx_hal_uart.o
    0x08005dea   0x08005dea   0x00000002   PAD
    0x08005dec   0x08005dec   0x0000000c   Code   RO          798    i.USART1_IRQHandler  stm32f4xx_it.o
    0x08005df8   0x08005df8   0x0000001c   Code   RO         4851    i.USART1_SEND       usartx.o
    0x08005e14   0x08005e14   0x0000000c   Code   RO          799    i.USART2_IRQHandler  stm32f4xx_it.o
    0x08005e20   0x08005e20   0x0000000c   Code   RO          800    i.USART3_IRQHandler  stm32f4xx_it.o
    0x08005e2c   0x08005e2c   0x0000001c   Code   RO         4852    i.USART3_SEND       usartx.o
    0x08005e48   0x08005e48   0x0000001c   Code   RO         4853    i.USART5_SEND       usartx.o
    0x08005e64   0x08005e64   0x00000002   Code   RO          801    i.UsageFault_Handler  stm32f4xx_it.o
    0x08005e66   0x08005e66   0x00000030   Code   RO         5861    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x08005e96   0x08005e96   0x00000020   Code   RO         1980    i.__NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08005eb6   0x08005eb6   0x00000002   PAD
    0x08005eb8   0x08005eb8   0x00000c50   Code   RO         5772    i.__hardfp_pow      m_wm.l(pow.o)
    0x08006b08   0x08006b08   0x0000007a   Code   RO         5786    i.__hardfp_sqrt     m_wm.l(sqrt.o)
    0x08006b82   0x08006b82   0x00000006   PAD
    0x08006b88   0x08006b88   0x00000080   Code   RO         5798    i.__hardfp_tan      m_wm.l(tan.o)
    0x08006c08   0x08006c08   0x00000438   Code   RO         5866    i.__ieee754_rem_pio2  m_wm.l(rred.o)
    0x08007040   0x08007040   0x000000f8   Code   RO         5863    i.__kernel_poly     m_wm.l(poly.o)
    0x08007138   0x08007138   0x00000350   Code   RO         5871    i.__kernel_tan      m_wm.l(tan_i.o)
    0x08007488   0x08007488   0x00000030   Code   RO         5841    i.__mathlib_dbl_divzero  m_wm.l(dunder.o)
    0x080074b8   0x080074b8   0x00000014   Code   RO         5842    i.__mathlib_dbl_infnan  m_wm.l(dunder.o)
    0x080074cc   0x080074cc   0x00000014   Code   RO         5843    i.__mathlib_dbl_infnan2  m_wm.l(dunder.o)
    0x080074e0   0x080074e0   0x00000020   Code   RO         5844    i.__mathlib_dbl_invalid  m_wm.l(dunder.o)
    0x08007500   0x08007500   0x00000020   Code   RO         5845    i.__mathlib_dbl_overflow  m_wm.l(dunder.o)
    0x08007520   0x08007520   0x00000020   Code   RO         5847    i.__mathlib_dbl_underflow  m_wm.l(dunder.o)
    0x08007540   0x08007540   0x00000002   Code   RO          710    i._sys_exit         usart.o
    0x08007542   0x08007542   0x00000002   PAD
    0x08007544   0x08007544   0x0000008c   Code   RO         4504    i.click_N_Double_MPU6050  key.o
    0x080075d0   0x080075d0   0x00000024   Code   RO         4856    i.data_task         usartx.o
    0x080075f4   0x080075f4   0x000002a0   Code   RO         4857    i.data_transition   usartx.o
    0x08007894   0x08007894   0x00000020   Code   RO         5503    i.delay_init        delay.o
    0x080078b4   0x080078b4   0x00000034   Code   RO         5504    i.delay_ms          delay.o
    0x080078e8   0x080078e8   0x00000034   Code   RO         5505    i.delay_us          delay.o
    0x0800791c   0x0800791c   0x00000018   Code   RO         5857    i.fabs              m_wm.l(fabs.o)
    0x08007934   0x08007934   0x00000010   Code   RO         5012    i.float_abs         balance.o
    0x08007944   0x08007944   0x00000030   Code   RO          711    i.fputc             usart.o
    0x08007974   0x08007974   0x00000094   Code   RO           15    i.main              main.o
    0x08007a08   0x08007a08   0x00000010   Code   RO         4598    i.oled_pow          oled.o
    0x08007a18   0x08007a18   0x00000884   Code   RO         5395    i.oled_show         show.o
    0x0800829c   0x0800829c   0x0000004c   Code   RO         5396    i.show_task         show.o
    0x080082e8   0x080082e8   0x0000006e   Code   RO         5788    i.sqrt              m_wm.l(sqrt.o)
    0x08008356   0x08008356   0x00000020   Code   RO         5015    i.target_limit_float  balance.o
    0x08008376   0x08008376   0x00000010   Code   RO         5016    i.target_limit_int  balance.o
    0x08008386   0x08008386   0x00000002   PAD
    0x08008388   0x08008388   0x00000010   Code   RO         4858    i.usart1_send       usartx.o
    0x08008398   0x08008398   0x00000014   Code   RO         4860    i.usart3_send       usartx.o
    0x080083ac   0x080083ac   0x00000010   Code   RO         4861    i.usart5_send       usartx.o
    0x080083bc   0x080083bc   0x00000018   Code   RO         5822    x$fpl$basic         fz_wm.l(basic.o)
    0x080083d4   0x080083d4   0x00000062   Code   RO         5732    x$fpl$d2f           fz_wm.l(d2f.o)
    0x08008436   0x08008436   0x00000002   PAD
    0x08008438   0x08008438   0x00000150   Code   RO         5734    x$fpl$dadd          fz_wm.l(daddsub_clz.o)
    0x08008588   0x08008588   0x00000010   Code   RO         5905    x$fpl$dcheck1       fz_wm.l(dcheck1.o)
    0x08008598   0x08008598   0x00000018   Code   RO         5907    x$fpl$dcmpinf       fz_wm.l(dcmpi.o)
    0x080085b0   0x080085b0   0x000002b0   Code   RO         5741    x$fpl$ddiv          fz_wm.l(ddiv.o)
    0x08008860   0x08008860   0x0000005e   Code   RO         5744    x$fpl$dfix          fz_wm.l(dfix.o)
    0x080088be   0x080088be   0x0000002e   Code   RO         5749    x$fpl$dflt          fz_wm.l(dflt_clz.o)
    0x080088ec   0x080088ec   0x00000026   Code   RO         5748    x$fpl$dfltu         fz_wm.l(dflt_clz.o)
    0x08008912   0x08008912   0x00000002   PAD
    0x08008914   0x08008914   0x00000078   Code   RO         5824    x$fpl$dleqf         fz_wm.l(dleqf.o)
    0x0800898c   0x0800898c   0x00000154   Code   RO         5754    x$fpl$dmul          fz_wm.l(dmul.o)
    0x08008ae0   0x08008ae0   0x0000009c   Code   RO         5826    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x08008b7c   0x08008b7c   0x0000000c   Code   RO         5828    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x08008b88   0x08008b88   0x0000006c   Code   RO         5830    x$fpl$drleqf        fz_wm.l(drleqf.o)
    0x08008bf4   0x08008bf4   0x00000016   Code   RO         5735    x$fpl$drsb          fz_wm.l(daddsub_clz.o)
    0x08008c0a   0x08008c0a   0x00000002   PAD
    0x08008c0c   0x08008c0c   0x00000198   Code   RO         5832    x$fpl$dsqrt         fz_wm.l(dsqrt_umaal.o)
    0x08008da4   0x08008da4   0x000001d4   Code   RO         5736    x$fpl$dsub          fz_wm.l(daddsub_clz.o)
    0x08008f78   0x08008f78   0x00000056   Code   RO         5756    x$fpl$f2d           fz_wm.l(f2d.o)
    0x08008fce   0x08008fce   0x0000008c   Code   RO         5834    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x0800905a   0x0800905a   0x0000000a   Code   RO         5998    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x08009064   0x08009064   0x0000000a   Code   RO         5836    x$fpl$fretinf       fz_wm.l(fretinf.o)
    0x0800906e   0x0800906e   0x00000064   Code   RO         5928    x$fpl$retnan        fz_wm.l(retnan.o)
    0x080090d2   0x080090d2   0x0000005c   Code   RO         5838    x$fpl$scalbn        fz_wm.l(scalbn.o)
    0x0800912e   0x0800912e   0x00000030   Code   RO         5983    x$fpl$trapveneer    fz_wm.l(trapv.o)
    0x0800915e   0x0800915e   0x00000000   Code   RO         5840    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x0800915e   0x0800915e   0x00000010   Data   RO         4371    .constdata          system_stm32f4xx.o
    0x0800916e   0x0800916e   0x00000008   Data   RO         4372    .constdata          system_stm32f4xx.o
    0x08009176   0x08009176   0x000014a4   Data   RO         4600    .constdata          oled.o
    0x0800a61a   0x0800a61a   0x00000006   PAD
    0x0800a620   0x0800a620   0x00000088   Data   RO         5775    .constdata          m_wm.l(pow.o)
    0x0800a6a8   0x0800a6a8   0x00000008   Data   RO         5865    .constdata          m_wm.l(qnan.o)
    0x0800a6b0   0x0800a6b0   0x000000c8   Data   RO         5868    .constdata          m_wm.l(rred.o)
    0x0800a778   0x0800a778   0x00000060   Data   RO         5872    .constdata          m_wm.l(tan_i.o)
    0x0800a7d8   0x0800a7d8   0x00000020   Data   RO         6056    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x10000000, Load base: 0x0800a838, Size: 0x00000000, Max: 0x00010000, ABSOLUTE)

    **** No section assigned to this execution region ****


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800a7f8, Size: 0x00001160, Max: 0x00020000, ABSOLUTE, COMPRESSED[0x00000040])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000004   Data   RW          294    .data               adc.o
    0x20000004   COMPRESSED   0x00000004   Data   RW          296    .data               adc.o
    0x20000008   COMPRESSED   0x00000001   Data   RW          713    .data               usart.o
    0x20000009   COMPRESSED   0x00000003   PAD
    0x2000000c   COMPRESSED   0x00000004   Data   RW          714    .data               usart.o
    0x20000010   COMPRESSED   0x0000000c   Data   RW         2124    .data               stm32f4xx_hal.o
    0x2000001c   COMPRESSED   0x00000004   Data   RW         4373    .data               system_stm32f4xx.o
    0x20000020   COMPRESSED   0x00000014   Data   RW         4505    .data               key.o
    0x20000034   COMPRESSED   0x00000004   Data   RW         4552    .data               led.o
    0x20000038   COMPRESSED   0x00000028   Data   RW         4703    .data               pstwo.o
    0x20000060   COMPRESSED   0x00000004   Data   RW         4805    .data               timer.o
    0x20000064   COMPRESSED   0x00000004   Data   RW         4806    .data               timer.o
    0x20000068   COMPRESSED   0x00000004   Data   RW         4807    .data               timer.o
    0x2000006c   COMPRESSED   0x00000004   Data   RW         4808    .data               timer.o
    0x20000070   COMPRESSED   0x00000010   Data   RW         4863    .data               usartx.o
    0x20000080   COMPRESSED   0x00000001   Data   RW         4864    .data               usartx.o
    0x20000081   COMPRESSED   0x00000001   Data   RW         4867    .data               usartx.o
    0x20000082   COMPRESSED   0x00000001   Data   RW         4869    .data               usartx.o
    0x20000083   COMPRESSED   0x00000001   PAD
    0x20000084   COMPRESSED   0x0000003c   Data   RW         5019    .data               balance.o
    0x200000c0   COMPRESSED   0x0000000c   Data   RW         5398    .data               show.o
    0x200000cc   COMPRESSED   0x00000001   Data   RW         5442    .data               system.o
    0x200000cd   COMPRESSED   0x00000001   Data   RW         5443    .data               system.o
    0x200000ce   COMPRESSED   0x00000002   PAD
    0x200000d0   COMPRESSED   0x00000004   Data   RW         5444    .data               system.o
    0x200000d4   COMPRESSED   0x00000004   Data   RW         5445    .data               system.o
    0x200000d8   COMPRESSED   0x00000004   Data   RW         5446    .data               system.o
    0x200000dc   COMPRESSED   0x00000001   Data   RW         5447    .data               system.o
    0x200000dd   COMPRESSED   0x00000001   Data   RW         5448    .data               system.o
    0x200000de   COMPRESSED   0x00000001   Data   RW         5449    .data               system.o
    0x200000df   COMPRESSED   0x00000001   Data   RW         5450    .data               system.o
    0x200000e0   COMPRESSED   0x00000001   Data   RW         5451    .data               system.o
    0x200000e1   COMPRESSED   0x00000003   PAD
    0x200000e4   COMPRESSED   0x00000004   Data   RW         5452    .data               system.o
    0x200000e8   COMPRESSED   0x00000004   Data   RW         5456    .data               system.o
    0x200000ec   COMPRESSED   0x00000004   Data   RW         5457    .data               system.o
    0x200000f0   COMPRESSED   0x00000004   Data   RW         5459    .data               system.o
    0x200000f4   COMPRESSED   0x00000004   Data   RW         5460    .data               system.o
    0x200000f8   COMPRESSED   0x00000004   Data   RW         5461    .data               system.o
    0x200000fc   COMPRESSED   0x00000004   Data   RW         5462    .data               system.o
    0x20000100   COMPRESSED   0x00000004   Data   RW         5463    .data               system.o
    0x20000104   COMPRESSED   0x00000004   Data   RW         5464    .data               system.o
    0x20000108   COMPRESSED   0x00000004   Data   RW         5465    .data               system.o
    0x2000010c   COMPRESSED   0x00000004   Data   RW         5466    .data               system.o
    0x20000110   COMPRESSED   0x00000004   Data   RW         5467    .data               system.o
    0x20000114   COMPRESSED   0x00000004   Data   RW         5468    .data               system.o
    0x20000118   COMPRESSED   0x00000001   Data   RW         5469    .data               system.o
    0x20000119   COMPRESSED   0x00000001   Data   RW         5470    .data               system.o
    0x2000011a   COMPRESSED   0x00000001   Data   RW         5471    .data               system.o
    0x2000011b   COMPRESSED   0x00000001   Data   RW         5472    .data               system.o
    0x2000011c   COMPRESSED   0x00000001   Data   RW         5473    .data               system.o
    0x2000011d   COMPRESSED   0x00000001   Data   RW         5474    .data               system.o
    0x2000011e   COMPRESSED   0x00000002   PAD
    0x20000120   COMPRESSED   0x00000004   Data   RW         5475    .data               system.o
    0x20000124   COMPRESSED   0x00000004   Data   RW         5476    .data               system.o
    0x20000128   COMPRESSED   0x00000004   Data   RW         5477    .data               system.o
    0x2000012c   COMPRESSED   0x00000004   Data   RW         5478    .data               system.o
    0x20000130   COMPRESSED   0x00000004   Data   RW         5479    .data               system.o
    0x20000134   COMPRESSED   0x00000004   Data   RW         5506    .data               delay.o
    0x20000138   COMPRESSED   0x0000000c   Data   RW         5588    .data               mpu6050.o
    0x20000144   COMPRESSED   0x00000006   Data   RW         5591    .data               mpu6050.o
    0x2000014a   COMPRESSED   0x00000006   Data   RW         5592    .data               mpu6050.o
    0x20000150        -       0x00000048   Zero   RW          292    .bss                adc.o
    0x20000198        -       0x00000028   Zero   RW          361    .bss                can.o
    0x200001c0        -       0x00000318   Zero   RW          580    .bss                tim.o
    0x200004d8        -       0x00000110   Zero   RW          712    .bss                usart.o
    0x200005e8        -       0x00000400   Zero   RW         4599    .bss                oled.o
    0x200009e8        -       0x00000009   Zero   RW         4702    .bss                pstwo.o
    0x200009f1   COMPRESSED   0x00000003   PAD
    0x200009f4        -       0x00000084   Zero   RW         4862    .bss                usartx.o
    0x20000a78        -       0x00000010   Zero   RW         5017    .bss                balance.o
    0x20000a88        -       0x00000018   Zero   RW         5180    .bss                robot_select_init.o
    0x20000aa0        -       0x0000000c   Zero   RW         5437    .bss                system.o
    0x20000aac        -       0x00000014   Zero   RW         5438    .bss                system.o
    0x20000ac0        -       0x00000014   Zero   RW         5439    .bss                system.o
    0x20000ad4        -       0x00000014   Zero   RW         5440    .bss                system.o
    0x20000ae8        -       0x00000014   Zero   RW         5441    .bss                system.o
    0x20000afc        -       0x00000060   Zero   RW         5910    .bss                c_w.l(libspace.o)
    0x20000b5c   COMPRESSED   0x00000004   PAD
    0x20000b60        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f407xx.o
    0x20000d60        -       0x00000400   Zero   RW            1    STACK               startup_stm32f407xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       414         66          0          8         72       3573   adc.o
      4570        642          0         60         16      14423   balance.o
       574         40          0          0         40       3911   can.o
       136         18          0          4          0       1687   delay.o
        64         12          0          0          0        655   encoder.o
       320         20          0          0          0       1071   gpio.o
       750         64          0          0          0       5958   i2c.o
       140          8          0         20          0       2435   key.o
        48          8          0          4          0        512   led.o
       300         18          0          0          0     720696   main.o
       448         12          0         24          0       5929   mpu6050.o
       826         34       5284          0       1024       7083   oled.o
       986        112          0         40          9       6356   pstwo.o
       504        142          0          0         24       3453   robot_select_init.o
      2568        400          0         12          0       3424   show.o
        64         26        392          0       1536        828   startup_stm32f407xx.o
       144         24          0         12          0       8669   stm32f4xx_hal.o
      1144         52          0          0          0       5741   stm32f4xx_hal_adc.o
       816          0          0          0          0       9883   stm32f4xx_hal_can.o
       198         14          0          0          0      33863   stm32f4xx_hal_cortex.o
       182          0          0          0          0       2055   stm32f4xx_hal_dma.o
       506         46          0          0          0       2152   stm32f4xx_hal_gpio.o
        52          4          0          0          0        838   stm32f4xx_hal_msp.o
      1344         72          0          0          0       5156   stm32f4xx_hal_rcc.o
      2556        164          0          0          0      18060   stm32f4xx_hal_tim.o
       232         28          0          0          0       3209   stm32f4xx_hal_tim_ex.o
      1370         16          0          0          0       9004   stm32f4xx_hal_uart.o
       152         66          0          0          0       9018   stm32f4xx_it.o
         0          0          0         97         92       3089   system.o
        16          4         24          4          0       1087   system_stm32f4xx.o
      2404        196          0          0        792      11501   tim.o
         0          0          0         16          0       1358   timer.o
       734        100          0          5        272       6109   usart.o
      1556        228          0         19        132       8938   usartx.o

    ----------------------------------------------------------------------
     26178       <USER>       <GROUP>        336       4012     921724   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        60          0          6         11          3          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        90          0          0          0          0          0   __dczerorl2.o
         8          0          0          0          0         68   __main.o
       104          0          0          0          0         84   __printf.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        28          0          0          0          0          0   __scatter_zi.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
       178          0          0          0          0         88   _printf_intcommon.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
        22          0          0          0          0        100   _rserrno.o
        18          0          0          0          0         80   exit.o
         8          0          0          0          0         68   ferror.o
         6          0          0          0          0        152   heapauxi.o
         2          0          0          0          0          0   libinit.o
         6          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       238          0          0          0          0        100   lludivv7m.o
        24          4          0          0          0         84   noretval__2printf.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
        78          0          0          0          0         80   rt_memclr_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        74          0          0          0          0         80   sys_stackheap_outer.o
        24          0          0          0          0        164   basic.o
        98          4          0          0          0        140   d2f.o
       826         16          0          0          0        492   daddsub_clz.o
        16          4          0          0          0        116   dcheck1.o
        24          0          0          0          0        116   dcmpi.o
       688        140          0          0          0        256   ddiv.o
        94          4          0          0          0        140   dfix.o
        84          0          0          0          0        232   dflt_clz.o
       120          4          0          0          0        140   dleqf.o
       340         12          0          0          0        152   dmul.o
       156          4          0          0          0        140   dnaninf.o
        12          0          0          0          0        116   dretinf.o
       108          0          0          0          0        128   drleqf.o
       408         56          0          0          0        168   dsqrt_umaal.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
        10          0          0          0          0        116   fpinit.o
        10          0          0          0          0        116   fretinf.o
       100          0          0          0          0        116   retnan.o
        92          0          0          0          0        116   scalbn.o
        48          0          0          0          0        116   trapv.o
         0          0          0          0          0          0   usenofp.o
       184         44          0          0          0        744   dunder.o
        24          0          0          0          0        124   fabs.o
        48          0          0          0          0        124   fpclassify.o
       248          0          0          0          0        152   poly.o
      3152        296        136          0          0        352   pow.o
         0          0          8          0          0          0   qnan.o
      1080        142        200          0          0        188   rred.o
       232          0          0          0          0        296   sqrt.o
       128         20          0          0          0        144   tan.o
       848         84         96          0          0        196   tan_i.o

    ----------------------------------------------------------------------
     10644        <USER>        <GROUP>          0        100       7052   Library Totals
        16          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1200         46          0          0         96       1388   c_w.l
      3484        252          0          0          0       3344   fz_wm.l
      5944        586        440          0          0       2320   m_wm.l

    ----------------------------------------------------------------------
     10644        <USER>        <GROUP>          0        100       7052   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     36822       3520       6178        336       4112     904252   Grand Totals
     36822       3520       6178         64       4112     904252   ELF Image Totals (compressed)
     36822       3520       6178         64          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                43000 (  41.99kB)
    Total RW  Size (RW Data + ZI Data)              4448 (   4.34kB)
    Total ROM Size (Code + RO Data + RW Data)      43064 (  42.05kB)

==============================================================================

