--cpu=Cortex-M4.fp.sp
"..\output\startup_stm32f407xx.o"
"..\output\main.o"
"..\output\gpio.o"
"..\output\adc.o"
"..\output\can.o"
"..\output\i2c.o"
"..\output\tim.o"
"..\output\usart.o"
"..\output\stm32f4xx_it.o"
"..\output\stm32f4xx_hal_msp.o"
"..\output\stm32f4xx_hal_adc.o"
"..\output\stm32f4xx_hal_adc_ex.o"
"..\output\stm32f4xx_ll_adc.o"
"..\output\stm32f4xx_hal_rcc.o"
"..\output\stm32f4xx_hal_rcc_ex.o"
"..\output\stm32f4xx_hal_flash.o"
"..\output\stm32f4xx_hal_flash_ex.o"
"..\output\stm32f4xx_hal_flash_ramfunc.o"
"..\output\stm32f4xx_hal_gpio.o"
"..\output\stm32f4xx_hal_dma_ex.o"
"..\output\stm32f4xx_hal_dma.o"
"..\output\stm32f4xx_hal_pwr.o"
"..\output\stm32f4xx_hal_pwr_ex.o"
"..\output\stm32f4xx_hal_cortex.o"
"..\output\stm32f4xx_hal.o"
"..\output\stm32f4xx_hal_exti.o"
"..\output\stm32f4xx_hal_can.o"
"..\output\stm32f4xx_hal_i2c.o"
"..\output\stm32f4xx_hal_i2c_ex.o"
"..\output\stm32f4xx_hal_tim.o"
"..\output\stm32f4xx_hal_tim_ex.o"
"..\output\stm32f4xx_hal_uart.o"
"..\output\system_stm32f4xx.o"
"..\output\datascope_dp.o"
"..\output\encoder.o"
"..\output\exti.o"
"..\output\key.o"
"..\output\led.o"
"..\output\oled.o"
"..\output\pstwo.o"
"..\output\timer.o"
"..\output\usartx.o"
"..\output\dma.o"
"..\output\balance.o"
"..\output\filter.o"
"..\output\robot_select_init.o"
"..\output\show.o"
"..\output\system.o"
"..\output\delay.o"
"..\output\sys.o"
"..\output\mpu6050.o"
--strict --scatter "..\Output\C30D.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "C30D.map" -o ..\Output\C30D.axf